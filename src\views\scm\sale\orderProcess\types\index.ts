// 订单统计数据接口
export interface OrderStatistics {
  saleOrders: number
  saleGrowth: number
  purchaseOrders: number
  purchaseGrowth: number
  workOrders: number
  workGrowth: number
  completedOrders: number
  completedGrowth: number
  exceptionOrders: number
  exceptionGrowth: number
}

// 通用订单接口（兼容销售订单和采购订单）
export interface UnifiedOrderVO {
  // 通用字段
  id?: number
  orderId?: number
  orderNo: string
  orderDate: Date
  totalAmount: number
  approvalStatus: string
  remark?: string
  remarks?: string

  // 销售订单特有字段
  customerName?: string
  customerId?: number
  estimatedDeliveryDate?: Date
  paymentStatus?: string
  invoiceStatus?: string
  closeStatus?: string
  trackingNumber?: string
  approvalNo?: string
  approverId?: string
  approverName?: string
  orderStatus?: string
  shippingAddress?: string
  billingAddress?: string
  orderSource?: string
  deliveryMethod?: string
  taxAmount?: number
  taxType?: string
  discountAmount?: number
  discountType?: string
  paymentMethod?: string
  salemanId?: string
  salemanName?: string
  salemanDeptId?: string
  salemanDeptName?: string
  kdId?: number
  orderDetails?: any[]

  // 采购订单特有字段
  supplierName?: string
  supplierId?: number
  deliveryDate?: Date
  status?: string
  isInStock?: boolean
  approveNo?: string
  approver?: string
  approvalTime?: Date

  // 工作订单特有字段
  workNo?: string
  planId?: number
  bomId?: number
  bomCode?: string
  bomVersion?: string
  scheduleStartDate?: string
  scheduleStartTime?: Date
  scheduleEndDate?: string
  scheduleEndTime?: Date
  scheduleQuantity?: number
  schedulePiece?: number
  scheduleCostTime?: string
  scheduleLine?: string
  scheduleHeadcount?: number
  requirement?: string
  actualLine?: string
  actualQuantity?: number
  actualStartTime?: Date
  actualEndTime?: Date
  actualCostTime?: string
  actualHeadcount?: string
  actualPiece?: number
  actualBatchNo?: string
  actualRemark?: string
  shareImageUrl?: string
  slotQuantity?: number
  slotCount?: number

  // 扩展字段
  productInfo?: string
  progress?: number
  isException?: boolean
  exceptionMessage?: string
  completedDate?: Date
  orderType?: 'sale' | 'purchase' | 'work' // 订单类型标识

  // 销售订单履约相关字段
  salesOrderInfo?: {
    orderNo?: string
    orderDate?: Date
    deliveryTime?: Date
    requirement?: string
    remark?: string
    customer?: {
      name?: string
      id?: number
    }
    product?: {
      name?: string
      fullCode?: string
      spec?: string
      quantity?: number
      unitName?: string
      specQuantity?: string
    }
    approvalStatus?: string
  }

  // 各流程环节数据
  request?: {
    status?: string
    createTime?: Date
    confirmTime?: Date
  }

  materialInventory?: {
    status?: number
    progress?: number
    rawMaterials?: Array<{
      materialId?: string
      fullCode?: string
      name?: string
      pendingQty?: number
      unitName?: string
      shortage?: boolean
      shortageQty?: number
    }>
  }

  procurement?: {
    progress?: number
    purchaseItems?: Array<{
      id?: string
      purchaseTime?: Date
      materialCode?: string
      materialName?: string
      purchaseStatus?: string
    }>
  }

  productionPlan?: {
    status?: string
    progress?: number
    quantity?: number
    fulfilledQty?: number
    workshop?: string
    productionLine?: string
    foreman?: string
    schedule?: Array<{
      startTime?: Date
      endTime?: Date
      quantity?: number
      fulfilledQty?: number
      status?: string
    }>
  }

  productionExecution?: {
    status?: string
    quantity?: number
    reportRecords?: Array<{
      reportTime?: Date
      worker?: string
      reportedQty?: number
      productionLine?: string
      temperature?: number
      humidity?: number
      memo?: string
    }>
  }

  qualityInspection?: {
    status?: string
    quantity?: number
    qualifiedQuantity?: number
    inspectedQuantity?: number
    inspector?: string
    inspectionRecords?: Array<{
      inspectTime?: Date
      inspector?: string
      inspectedQty?: number
      result?: string
      memo?: string
    }>
  }

  warehousing?: {
    status?: string
    quantity?: number
    warehouseRecords?: Array<{
      warehouseTime?: Date
      warehouseName?: string
      operator?: string
      inboundQty?: number
      approveStatus?: string
      memo?: string
    }>
  }

  delivery?: {
    status?: string
    quantity?: number
    deliveryRecords?: Array<{
      deliveryTime?: Date
      quantity?: number
      logistics?: {
        customerName?: string
        customerContact?: string
        customerPhone?: string
        customerAddress?: string
        logisticsCompany?: string
        transportMethod?: string
        logisticsTrackingNumber?: string
      }
      memo?: string
    }>
  }

  outbound?: {
    status?: string
    quantity?: number
    outboundRecords?: Array<{
      outboundTime?: Date
      outboundQty?: number
      operator?: string
      outboundNo?: string
      memo?: string
    }>
  }
}

// 查询参数接口
export interface QueryParams {
  pageNo: number
  pageSize: number
  orderNo: string // 订单编号
  customerName: string // 客户/供应商名称
  approvalStatus: string
  orderStatus: string
  paymentStatus: string
  deliveryStatus: string
  invoiceStatus: string
  timeRange: string | string[] // 时间范围，可以是字符串或字符串数组
  customerType: string
  productType: string
}

// 视图类型
export type ViewType = 'table'

// 排序类型
export type SortType = 'newest' | 'oldest' | 'delivery-date' | 'amount'
