<template>
  <div>
    <el-card shadow="never">
      <el-skeleton :loading="loading" animated>
        <el-row :gutter="16" justify="space-between">
          <el-col :xl="12" :lg="12" :md="12" :sm="24" :xs="24">
            <div class="flex items-center">
              <el-avatar :src="avatar" :size="70" class="mr-16px">
                <img src="@/assets/imgs/avatar.gif" alt="" />
              </el-avatar>
              <div>
                <div class="text-20px">
                  {{ t('workplace.welcome') }} {{ username }} {{ t('workplace.happyDay') }}
                </div>
                <div class="mt-10px text-14px text-gray-500">
                  {{ weatherInfo }}
                </div>
              </div>
            </div>
          </el-col>
          <el-col :xl="12" :lg="12" :md="12" :sm="24" :xs="24">
            <div class="h-70px flex items-center justify-end lt-sm:mt-10px">
              <div class="px-8px text-right">
                <div class="mb-16px text-14px text-gray-400">{{ t('workplace.orderNum') }}</div>
                <CountTo
                  class="text-20px"
                  :start-val="0"
                  :end-val="totalSate.project"
                  :duration="2600"
                />
              </div>
              <el-divider direction="vertical" />
              <div class="px-8px text-right">
                <div class="mb-16px text-14px text-gray-400">{{ t('workplace.manufactureNum') }}</div>
                <CountTo
                  class="text-20px"
                  :start-val="0"
                  :end-val="totalSate.todo"
                  :duration="2600"
                />
              </div>
              <el-divider direction="vertical" border-style="dashed" />
              <div class="px-8px text-right">
                <div class="mb-16px text-14px text-gray-400">{{ t('workplace.deliveryNum') }}</div>
                <CountTo
                  class="text-20px"
                  :start-val="0"
                  :end-val="totalSate.access"
                  :duration="2600"
                />
              </div>
            </div>
          </el-col>
        </el-row>
      </el-skeleton>
    </el-card>
  </div>

  <el-row class="mt-8px" :gutter="8" justify="space-between">
    <el-col :xl="16" :lg="16" :md="24" :sm="24" :xs="24" class="mb-8px">
      <el-card shadow="never">
        <template #header>
          <div class="h-3 flex justify-between">
            <span>{{ t('workplace.stats') }}</span>
            <el-link
              type="primary"
              :underline="false"
              href="https://github.com/yudaocode"
              target="_blank"
            >
              {{ t('action.more') }}
            </el-link>
          </div>
        </template>
        <el-skeleton :loading="loading" animated>
          <el-row>
            <el-col
              v-for="(item, index) in projects"
              :key="`card-${index}`"
              :xl="8"
              :lg="8"
              :md="8"
              :sm="24"
              :xs="24"
            >
              <el-card
                shadow="hover"
                class="mr-5px mt-5px cursor-pointer"
                @click="handleProjectClick(item.message)"
              >
                <div class="flex items-center">
                  <Icon
                    :icon="item.icon"
                    :size="25"
                    class="mr-8px"
                    :style="{ color: item.color }"
                  />
                  <span class="text-16px">{{ item.name }}</span>
                </div>
                <div class="mt-12px text-12px text-gray-400">{{ t(item.message) }}</div>
                <div class="mt-12px flex justify-between text-12px text-gray-400">
                  <span>{{ item.personal }}</span>
                  <span>{{ formatTime(item.time, 'yyyy-MM-dd') }}</span>
                </div>
              </el-card>
            </el-col>
          </el-row>
        </el-skeleton>
      </el-card>

      <el-card shadow="never" class="mt-8px">
        <el-skeleton :loading="loading" animated>
          <el-row :gutter="20" justify="space-between">
            <el-col :xl="10" :lg="10" :md="24" :sm="24" :xs="24">
              <el-card shadow="hover" class="mb-8px">
                <el-skeleton :loading="loading" animated>
                  <Echart :options="pieOptionsData" :height="280" />
                </el-skeleton>
              </el-card>
            </el-col>
            <el-col :xl="14" :lg="14" :md="24" :sm="24" :xs="24">
              <el-card shadow="hover" class="mb-8px">
                <el-skeleton :loading="loading" animated>
                  <Echart :options="barOptionsData" :height="280" />
                </el-skeleton>
              </el-card>
            </el-col>
          </el-row>
        </el-skeleton>
      </el-card>
    </el-col>
    <el-col :xl="8" :lg="8" :md="24" :sm="24" :xs="24" class="mb-8px">
      <el-card shadow="never">
        <template #header>
          <div class="h-3 flex justify-between">
            <span>{{ t('workplace.shortcutOperation') }}</span>
          </div>
        </template>
        <el-skeleton :loading="loading" animated>
          <el-row>
            <el-col v-for="item in shortcut" :key="`team-${item.name}`" :span="8" class="mb-8px">
              <div class="flex items-center">
                <Icon :icon="item.icon" class="mr-8px" :style="{ color: item.color }" />
                <el-link type="default" :underline="false" @click="handleShortcutClick(item.url)">
                  {{ item.name }}
                </el-link>
              </div>
            </el-col>
          </el-row>
        </el-skeleton>
      </el-card>
      <el-card shadow="never" class="mt-8px">
        <template #header>
          <div class="h-3 flex justify-between">
            <span>{{ t('workplace.notice') }}</span>
            <el-link type="primary" :underline="false">{{ t('action.more') }}</el-link>
          </div>
        </template>
        <el-skeleton :loading="loading" animated>
          <div v-for="(item, index) in notice" :key="`dynamics-${index}`">
            <div class="flex items-center">
              <el-avatar :src="avatar" :size="35" class="mr-16px">
                <img src="@/assets/imgs/avatar.gif" alt="" />
              </el-avatar>
              <div>
                <div class="text-14px">
                  <Highlight :keys="item.keys.map((v) => t(v))">
                    {{ item.type }} : {{ item.title }}
                  </Highlight>
                </div>
                <div class="mt-16px text-12px text-gray-400">
                  {{ formatTime(item.date, 'yyyy-MM-dd') }}
                </div>
              </div>
            </div>
            <el-divider />
          </div>
        </el-skeleton>
      </el-card>
    </el-col>
  </el-row>


</template>
<script lang="ts" setup>
import { set } from 'lodash-es'
import { EChartsOption } from 'echarts'
import { formatTime } from '@/utils'

import { useUserStore } from '@/store/modules/user'
// import { useWatermark } from '@/hooks/web/useWatermark'
import type { WorkplaceTotal, Project, Notice, Shortcut } from './types'
import { pieOptions, barOptions } from './echarts-data'
import { useRouter } from 'vue-router'

defineOptions({ name: 'Index' })

const { t } = useI18n()
const router = useRouter()
const userStore = useUserStore()
// const { setWatermark } = useWatermark()
const loading = ref(true)
const avatar = userStore.getUser.avatar
const username = userStore.getUser.nickname
const weatherInfo = ref('正在获取天气信息...')
const pieOptionsData = reactive<EChartsOption>(pieOptions) as EChartsOption

// 天气缓存配置
const WEATHER_CACHE_KEY = 'weather_cache_data'
const WEATHER_CACHE_DURATION = 60 * 60 * 1000 // 1小时缓存时间



// 天气缓存接口
interface WeatherCache {
  data: string
  timestamp: number
}



// 获取缓存的天气数据
const getCachedWeather = (): string | null => {
  try {
    const cached = localStorage.getItem(WEATHER_CACHE_KEY)
    if (!cached) return null

    const cacheData: WeatherCache = JSON.parse(cached)
    const now = Date.now()

    // 检查缓存是否过期
    if (now - cacheData.timestamp > WEATHER_CACHE_DURATION) {
      localStorage.removeItem(WEATHER_CACHE_KEY)
      return null
    }

    return cacheData.data
  } catch (error) {
    console.error('读取天气缓存失败:', error)
    localStorage.removeItem(WEATHER_CACHE_KEY)
    return null
  }
}

// 设置天气缓存
const setCachedWeather = (data: string): void => {
  try {
    const cacheData: WeatherCache = {
      data,
      timestamp: Date.now()
    }
    localStorage.setItem(WEATHER_CACHE_KEY, JSON.stringify(cacheData))
  } catch (error) {
    console.error('设置天气缓存失败:', error)
  }
}


// 获取统计数
let totalSate = reactive<WorkplaceTotal>({
  project: 0,
  access: 0,
  todo: 0
})

const getCount = async () => {
  const data = {
    project: 40,
    access: 2340,
    todo: 10
  }
  totalSate = Object.assign(totalSate, data)
}

// 获取项目数
let projects = reactive<Project[]>([])
const getProject = async () => {
  const data = [
    // {
    //   name: 'ruoyi-vue-pro',
    //   icon: 'simple-icons:springboot',
    //   message: 'github.com/YunaiV/ruoyi-vue-pro',
    //   personal: 'Spring Boot 单体架构',
    //   time: new Date('2025-01-02'),
    //   color: '#6DB33F'
    // },
    // {
    //   name: 'yudao-ui-admin-vue3',
    //   icon: 'ep:element-plus',
    //   message: 'github.com/yudaocode/yudao-ui-admin-vue3',
    //   personal: 'Vue3 + element-plus 管理后台',
    //   time: new Date('2025-02-03'),
    //   color: '#409EFF'
    // },
    // {
    //   name: 'yudao-ui-mall-uniapp',
    //   icon: 'icon-park-outline:mall-bag',
    //   message: 'github.com/yudaocode/yudao-ui-mall-uniapp',
    //   personal: 'Vue3 + uniapp 商城手机端',
    //   time: new Date('2025-03-04'),
    //   color: '#ff4d4f'
    // },
    // {
    //   name: 'yudao-cloud',
    //   icon: 'material-symbols:cloud-outline',
    //   message: 'github.com/YunaiV/yudao-cloud',
    //   personal: 'Spring Cloud 微服务架构',
    //   time: new Date('2025-04-05'),
    //   color: '#1890ff'
    // },
    // {
    //   name: 'yudao-ui-admin-vben',
    //   icon: 'devicon:antdesign',
    //   message: 'github.com/yudaocode/yudao-ui-admin-vben',
    //   personal: 'Vue3 + vben5(antd) 管理后台',
    //   time: new Date('2025-05-06'),
    //   color: '#e18525'
    // },
    // {
    //   name: 'yudao-ui-admin-uniapp',
    //   icon: 'ant-design:mobile',
    //   message: 'github.com/yudaocode/yudao-ui-admin-uniapp',
    //   personal: 'Vue3 + uniapp 管理手机端',
    //   time: new Date('2025-06-01'),
    //   color: '#2979ff'
    // }
  ]
  projects = Object.assign(projects, data)
}

// 获取通知公告
let notice = reactive<Notice[]>([])
const getNotice = async () => {
  const data = [
    // {
    //   title: '系统支持 JDK 8/17/21，Vue 2/3',
    //   type: '技术兼容性',
    //   keys: ['JDK', 'Vue'],
    //   date: new Date()
    // },
    // {
    //   title: '后端提供 Spring Boot 2.7/3.2 + Cloud 双架构',
    //   type: '架构灵活性',
    //   keys: ['Boot', 'Cloud'],
    //   date: new Date()
    // },
    // {
    //   title: '全部开源，个人与企业可 100% 直接使用，无需授权',
    //   type: '开源免授权',
    //   keys: ['无需授权'],
    //   date: new Date()
    // },
    // {
    //   title: '国内使用最广泛的快速开发平台，远超 10w+ 企业使用',
    //   type: '广泛企业认可',
    //   keys: ['最广泛', '10w+'],
    //   date: new Date()
    // }
  ]
  notice = Object.assign(notice, data)
}

// 获取快捷入口
let shortcut = reactive<Shortcut[]>([])

const getShortcut = async () => {
  const data = [
    {
      name: '首页',
      icon: 'ion:home-outline',
      url: '/',
      color: '#1fdaca'
    },
    {
      name: '商城中心',
      icon: 'ep:shop',
      url: '/mall/home',
      color: '#ff6b6b'
    },
    {
      name: 'AI 大模型',
      icon: 'tabler:ai',
      url: '/ai/chat',
      color: '#7c3aed'
    },
    {
      name: 'SCM 系统',
      icon: 'simple-icons:erpnext',
      url: '/operate/operate/home',
      color: '#3fb27f'
    },
    {
      name: 'CRM 系统',
      icon: 'simple-icons:civicrm',
      url: '/crm/backlog',
      color: '#4daf1bc9'
    },
    {
      name: 'IoT 物联网',
      icon: 'fa-solid:hdd',
      url: '/iot/home',
      color: '#1a73e8'
    }
  ]
  shortcut = Object.assign(shortcut, data)
}

// 用户来源
const getUserAccessSource = async () => {
  const data = [
    { value: 335, name: 'analysis.directAccess' },
    { value: 310, name: 'analysis.mailMarketing' },
    { value: 234, name: 'analysis.allianceAdvertising' },
    { value: 135, name: 'analysis.videoAdvertising' },
    { value: 1548, name: 'analysis.searchEngines' }
  ]
  set(
    pieOptionsData,
    'legend.data',
    data.map((v) => t(v.name))
  )
  pieOptionsData!.series![0].data = data.map((v) => {
    return {
      name: t(v.name),
      value: v.value
    }
  })
}
const barOptionsData = reactive<EChartsOption>(barOptions) as EChartsOption

// 周活跃量
const getWeeklyUserActivity = async () => {
  const data = [
    { value: 13253, name: 'analysis.monday' },
    { value: 34235, name: 'analysis.tuesday' },
    { value: 26321, name: 'analysis.wednesday' },
    { value: 12340, name: 'analysis.thursday' },
    { value: 24643, name: 'analysis.friday' },
    { value: 1322, name: 'analysis.saturday' },
    { value: 1324, name: 'analysis.sunday' }
  ]
  set(
    barOptionsData,
    'xAxis.data',
    data.map((v) => t(v.name))
  )
  set(barOptionsData, 'series', [
    {
      name: t('analysis.activeQuantity'),
      data: data.map((v) => v.value),
      type: 'bar'
    }
  ])
}

// 获取用户地理位置
const getUserLocation = (): Promise<{ latitude: number; longitude: number }> => {
  return new Promise((resolve, reject) => {
    if (!navigator.geolocation) {
      reject(new Error('浏览器不支持地理位置获取'))
      return
    }

    navigator.geolocation.getCurrentPosition(
      (position) => {
        resolve({
          latitude: position.coords.latitude,
          longitude: position.coords.longitude
        })
      },
      (error) => {
        let errorMessage = '无法获取位置信息'

        switch (error.code) {
          case error.PERMISSION_DENIED:
            errorMessage = '无法获取位置信息，请允许访问位置权限'
            break
          case error.POSITION_UNAVAILABLE:
            errorMessage = '位置信息不可用'
            break
          case error.TIMEOUT:
            errorMessage = '获取位置信息超时'
            break
          default:
            errorMessage = '获取位置信息失败'
            break
        }

        reject(new Error(errorMessage))
      },
      {
        enableHighAccuracy: true,
        timeout: 10000,
        maximumAge: 300000 // 5分钟缓存
      }
    )
  })
}



// 使用高德逆地理编码API将坐标转换为中文地址
const reverseGeocode = async (longitude: number, latitude: number): Promise<string | null> => {
  try {
    const AMAP_KEY = '509986f5eb000c56fc253de628409dc0'
    const controller = new AbortController()
    const timeoutId = setTimeout(() => controller.abort(), 5000) // 5秒超时

    const response = await fetch(
      `https://restapi.amap.com/v3/geocode/regeo?key=${AMAP_KEY}&location=${longitude},${latitude}&extensions=all&s=rsx&sdkversion=sdkversion&logversion=logversion`,
      {
        signal: controller.signal,
        headers: {
          'Accept': 'application/json'
        }
      }
    )

    clearTimeout(timeoutId)

    if (!response.ok) {
      throw new Error('逆地理编码请求失败')
    }

    const data = await response.json()

    if (data.status === '1' && data.regeocode && data.regeocode.addressComponent) {
      const addressComponent = data.regeocode.addressComponent
      const province = addressComponent.province || ''
      const city = addressComponent.city || addressComponent.district || ''
      const district = addressComponent.district || ''

      // 构建中文地址，优先显示市级
      let chineseAddress = ''
      if (city && city !== province) {
        // 如果城市名不等于省份名，优先显示城市
        chineseAddress = city.replace('市', '') + '市'
      } else if (district) {
        // 如果没有市级信息，使用区县
        chineseAddress = district.replace('区', '').replace('县', '') + (district.includes('区') ? '区' : '县')
      } else if (province) {
        // 最后使用省份
        chineseAddress = province.replace('省', '') + '省'
      }

      return chineseAddress || null
    }

    throw new Error('逆地理编码返回数据异常')
  } catch (error) {
    return null
  }
}

// 使用ipapi.co获取定位信息（更准确的IP定位）
const getIpapiLocation = async (): Promise<{ address: string; longitude: number; latitude: number; rawData?: any } | null> => {
  try {
    const controller = new AbortController()
    const timeoutId = setTimeout(() => controller.abort(), 6000) // 6秒超时

    const response = await fetch('https://ipapi.co/json/', {
      signal: controller.signal,
      headers: {
        'Accept': 'application/json'
      }
    })

    clearTimeout(timeoutId)

    if (!response.ok) {
      throw new Error(`ipapi.co请求失败: ${response.status} ${response.statusText}`)
    }

    const data = await response.json()

    if (data.latitude && data.longitude) {
      const longitude = parseFloat(data.longitude)
      const latitude = parseFloat(data.latitude)

      // 尝试使用逆地理编码获取中文地址
      const chineseAddress = await reverseGeocode(longitude, latitude)

      // 如果逆地理编码成功，使用中文地址；否则使用原始英文地址
      let finalAddress = chineseAddress
      if (!finalAddress && data.city) {
        // 降级：使用英文城市名
        finalAddress = data.city
      }
      if (!finalAddress) {
        finalAddress = '未知位置'
      }

      const locationResult = {
        address: finalAddress,
        longitude,
        latitude,
        rawData: {
          name: data.city,
          region: data.region,
          country: data.country_name,
          timezone: data.timezone,
          org: data.org,
          chineseAddress: chineseAddress
        }
      }

      return locationResult
    }

    throw new Error('ipapi.co定位失败：返回数据格式异常')
  } catch (error: any) {
    return null
  }
}

// 使用天气API的自动定位功能
const getWeatherApiLocation = async (): Promise<{ address: string; longitude: number; latitude: number; rawData?: any } | null> => {
  try {
    const API_KEY = 'c63bd1debc864b85ae2235910252306'
    const url = `https://api.weatherapi.com/v1/current.json?key=${API_KEY}&q=auto:ip&lang=zh&aqi=no`

    // 使用天气API的自动IP定位功能，添加超时控制
    const controller = new AbortController()
    const timeoutId = setTimeout(() => controller.abort(), 8000) // 8秒超时

    const response = await fetch(url, {
      signal: controller.signal,
      headers: {
        'Accept': 'application/json'
      }
    })

    clearTimeout(timeoutId)

    if (!response.ok) {
      throw new Error(`天气API自动定位请求失败: ${response.status} ${response.statusText}`)
    }

    const data = await response.json()

    if (data.location && data.location.lat && data.location.lon) {
      const region = data.location.region || ''
      const name = data.location.name || ''
      const country = data.location.country || ''
      const longitude = parseFloat(data.location.lon)
      const latitude = parseFloat(data.location.lat)

      // 尝试使用逆地理编码获取中文地址
      const chineseAddress = await reverseGeocode(longitude, latitude)

      // 确定最终显示的地址
      let finalAddress = chineseAddress || ''
      if (!finalAddress) {
        // 降级：优先使用城市名，然后是地区名
        if (name && name !== region) {
          finalAddress = name
        } else if (region) {
          finalAddress = region
        } else {
          finalAddress = '未知位置'
        }
      }

      const locationResult = {
        address: finalAddress,
        longitude,
        latitude,
        rawData: {
          name,
          region,
          country,
          tz_id: data.location.tz_id,
          localtime: data.location.localtime,
          chineseAddress: chineseAddress
        }
      }

      return locationResult
    }

    throw new Error('天气API自动定位失败：返回数据格式异常')
  } catch (error: any) {
    return null
  }
}

// 使用高德API获取定位信息（包含地址和坐标）
const getAmapLocation = async (): Promise<{ address: string; longitude: number; latitude: number } | null> => {
  try {
    const AMAP_KEY = '509986f5eb000c56fc253de628409dc0'
    const controller = new AbortController()
    const timeoutId = setTimeout(() => controller.abort(), 6000) // 6秒超时

    const response = await fetch(
      `https://restapi.amap.com/v3/ip?key=${AMAP_KEY}&output=json`,
      {
        signal: controller.signal,
        headers: {
          'Accept': 'application/json'
        }
      }
    )

    clearTimeout(timeoutId)

    if (!response.ok) {
      throw new Error('高德API请求失败')
    }

    const data = await response.json()

    // 检查API返回状态
    if (data.status !== '1') {
      throw new Error(`高德API错误: ${data.info || '未知错误'}`)
    }

    // 检查返回的数据是否有效（处理空数组的情况）
    const hasValidCity = data.city && !Array.isArray(data.city) && data.city.length > 0
    const hasValidRectangle = data.rectangle && !Array.isArray(data.rectangle) && data.rectangle.length > 0

    if (!hasValidCity || !hasValidRectangle) {
      throw new Error('IP定位失败，无法获取位置信息')
    }

    // 解析经纬度坐标（rectangle格式：经度1,纬度1;经度2,纬度2）
    const rectangle = data.rectangle.split(';')[0] // 取第一个坐标点
    const [longitude, latitude] = rectangle.split(',').map(Number)

    if (isNaN(longitude) || isNaN(latitude)) {
      throw new Error('坐标解析失败')
    }

    // 构建中文地址
    const province = data.province || ''
    const city = data.city || ''

    let address = ''
    // 如果城市名包含省份名，则只返回城市名
    if (city.includes(province.replace('省', '').replace('市', ''))) {
      address = city
    } else {
      // 否则返回省份+城市
      address = province === city ? city : `${province} ${city}`
    }

    return {
      address,
      longitude,
      latitude
    }

  } catch (error) {
    return null
  }
}

// 获取天气信息
const getWeatherInfo = async () => {
  try {
    // 首先检查缓存
    const cachedWeather = getCachedWeather()
    if (cachedWeather) {
      weatherInfo.value = cachedWeather
      return
    }

    // 显示加载状态
    weatherInfo.value = '正在获取天气信息...'

    // 缓存不存在或已过期，重新获取
    let locationDisplay = ''
    let coordinates = { latitude: 0, longitude: 0 }
    let locationInfo: { address: string; longitude: number; latitude: number; rawData?: any } | null = null

    // 1. 最高优先级：尝试浏览器地理位置API
    try {
      coordinates = await getUserLocation()
      locationDisplay = '当前位置'
    } catch (geoError) {
      // 2. 第二优先级：尝试高德API定位
      try {
        locationInfo = await getAmapLocation()

        if (locationInfo) {
          locationDisplay = locationInfo.address
          coordinates = {
            latitude: locationInfo.latitude,
            longitude: locationInfo.longitude
          }
        } else {
          throw new Error('高德API返回空结果')
        }
      } catch (amapError) {
        // 3. 第三优先级：尝试ipapi.co定位（更准确的IP定位）
        try {
          locationInfo = await getIpapiLocation()

          if (locationInfo) {
            // 直接使用已经转换好的中文地址
            locationDisplay = locationInfo.address
            coordinates = {
              latitude: locationInfo.latitude,
              longitude: locationInfo.longitude
            }
          } else {
            throw new Error('ipapi.co定位返回空结果')
          }
        } catch (ipapiError) {
          // 4. 第四优先级：尝试天气API自动定位
          try {
            locationInfo = await getWeatherApiLocation()

            if (locationInfo) {
              // 直接使用已经转换好的中文地址
              locationDisplay = locationInfo.address
              coordinates = {
                latitude: locationInfo.latitude,
                longitude: locationInfo.longitude
              }
            } else {
              throw new Error('天气API自动定位返回空结果')
            }
          } catch (weatherError) {
            // 最后降级：使用默认位置
            coordinates = { latitude: 39.916527, longitude: 116.397128 }
            locationDisplay = '北京市'
          }
        }
      }
    }

    // 使用获取到的坐标调用WeatherAPI，添加超时控制
    const API_KEY = 'c63bd1debc864b85ae2235910252306'
    const controller = new AbortController()
    const timeoutId = setTimeout(() => controller.abort(), 10000) // 10秒超时

    const response = await fetch(
      `https://api.weatherapi.com/v1/current.json?key=${API_KEY}&q=${coordinates.latitude},${coordinates.longitude}&lang=zh&aqi=no`,
      {
        signal: controller.signal,
        headers: {
          'Accept': 'application/json'
        }
      }
    )

    clearTimeout(timeoutId)

    if (!response.ok) {
      throw new Error('天气API请求失败')
    }

    const data = await response.json()

    const temp = Math.round(data.current.temp_c)
    const feelsLike = Math.round(data.current.feelslike_c)
    const condition = data.current.condition.text

    // 如果位置显示为默认值，则使用WeatherAPI的地址信息进行补充
    if (locationDisplay === '当前位置' || locationDisplay === '北京市') {
      const region = data.location.region || ''
      const name = data.location.name || ''
      if (region || name) {
        locationDisplay = region || name || locationDisplay
      }
    }

    // 更新天气信息显示，地址放在最前面
    const weatherDisplay = `${locationDisplay} · ${condition}，${temp}℃，体感 ${feelsLike}℃`
    weatherInfo.value = weatherDisplay

    // 缓存天气信息
    setCachedWeather(weatherDisplay)

  } catch (error: any) {
    console.error('获取天气信息失败:', error)

    // 根据错误类型显示不同的提示信息
    if (error?.name === 'AbortError') {
      weatherInfo.value = '天气信息获取超时，请稍后刷新'
    } else if (error?.message?.includes('请允许访问位置权限')) {
      weatherInfo.value = '无法获取位置信息，请允许访问位置权限'
    } else if (error?.message?.includes('位置信息不可用')) {
      weatherInfo.value = '位置信息不可用，请检查设备定位服务'
    } else if (error?.message?.includes('获取位置信息超时')) {
      weatherInfo.value = '获取位置信息超时，请稍后重试'
    } else if (error?.message?.includes('浏览器不支持')) {
      weatherInfo.value = '浏览器不支持地理位置获取'
    } else if (error?.message?.includes('无法获取位置信息')) {
      weatherInfo.value = '无法获取位置信息，请允许访问位置权限'
    } else if (error?.message?.includes('API') || error?.message?.includes('网络')) {
      weatherInfo.value = '天气服务暂时不可用'
    } else {
      weatherInfo.value = '20℃ - 32℃！' // 降级到默认显示
    }
  }
}

const getAllApi = async () => {
  // 先加载主要内容，不等待天气信息
  await Promise.all([
    getCount(),
    getProject(),
    getNotice(),
    getShortcut(),
    getUserAccessSource(),
    getWeeklyUserActivity()
  ])
  loading.value = false

  // 异步加载天气信息，不阻塞页面渲染
  getWeatherInfo().catch(error => {
    console.error('天气信息加载失败:', error)
  })
}

const handleProjectClick = (message: string) => {
  window.open(`https://${message}`, '_blank')
}

const handleShortcutClick = (url: string) => {
  router.push(url)
}

getAllApi()
</script>
