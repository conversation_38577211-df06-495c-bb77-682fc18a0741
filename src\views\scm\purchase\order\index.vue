<template>
  <ContentWrap>
    <!-- 搜索工作栏 -->
    <el-form
      class="-mb-15px"
      :model="queryParams"
      ref="queryFormRef"
      :inline="true"
      label-width="auto"
    >
      <!-- 基础搜索项 - 始终显示 -->
       <el-form-item label="物料名称" prop="materialName">
          <el-input
            v-model="queryParams.materialName"
            placeholder="请输入物料名称"
            clearable
            @keyup.enter="handleQuery"
            class="!w-240px"
          />
        </el-form-item>
        <el-form-item label="物料编码" prop="materialCode">
          <el-input
            v-model="queryParams.materialCode"
            placeholder="请输入物料编码"
            clearable
            @keyup.enter="handleQuery"
            class="!w-240px"
          />
        </el-form-item>
      <el-form-item label="供应商名称" prop="supplierName">
        <el-input
          v-model="queryParams.supplierName"
          placeholder="请输入供应商名称"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      

      <!-- 高级搜索项 - 可展开收起 -->
      <template v-if="isExpanded">
        <el-form-item label="订单编号" prop="orderNo">
        <el-input
          v-model="queryParams.orderNo"
          placeholder="请输入订单编号"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="订单日期" prop="orderDate">
        <el-date-picker
          v-model="queryParams.orderDate"
          value-format="YYYY-MM-DD HH:mm:ss"
          type="daterange"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
          class="!w-240px"
        />
      </el-form-item>
        <el-form-item label="交货日期" prop="deliveryDate">
          <el-date-picker
            v-model="queryParams.deliveryDate"
            value-format="YYYY-MM-DD HH:mm:ss"
            type="daterange"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
            class="!w-240px"
          />
        </el-form-item>
        <el-form-item label="创建时间" prop="createTime">
          <el-date-picker
            v-model="queryParams.createTime"
            value-format="YYYY-MM-DD HH:mm:ss"
            type="daterange"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
            class="!w-240px"
          />
        </el-form-item>
        <el-form-item label="入库" prop="isInStock">
          <el-select
            v-model="queryParams.isInStock"
            placeholder="请选择入库"
            clearable
            class="!w-240px"
          >
            <el-option
              v-for="dict in getStrDictOptions(DICT_TYPE.COMMON_YES_NO)"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="审批单号" prop="approveNo">
          <el-input
            v-model="queryParams.approveNo"
            placeholder="请输入审批单号"
            clearable
            @keyup.enter="handleQuery"
            class="!w-240px"
          />
        </el-form-item>
        <el-form-item label="审批状态" prop="approvalStatus">
          <el-select
            v-model="queryParams.approvalStatus"
            placeholder="请选择审批状态"
            clearable
            class="!w-240px"
          >
            <el-option
              v-for="dict in getStrDictOptions(DICT_TYPE.APPROVE_STATUS)"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
      </template>

      <!-- 操作按钮行 -->
      <el-form-item>
        <el-button @click="handleQuery" class="ml-4"><Icon icon="ep:search" class="mr-5px" /> 搜索</el-button>
        <el-button @click="resetQuery"><Icon icon="ep:refresh" class="mr-5px" /> 重置</el-button>
        <el-button
          type="primary"
          plain
          @click="openForm('create')"
          v-hasPermi="['purchase:order:create']"
        >
          <Icon icon="ep:plus" class="mr-5px" /> 新增
        </el-button>
        <el-button
          type="success"
          plain
          @click="handleExport"
          :loading="exportLoading"
          v-hasPermi="['purchase:order:export']"
        >
          <Icon icon="ep:download" class="mr-5px" /> 导出
        </el-button>
        <el-button
          type="primary"
          plain
          @click="handleApproval"
          :loading="exportLoading"
          v-hasPermi="['purchase:order:approve']"
        >
          <Icon icon="ep:check" class="mr-5px" /> 审核
        </el-button>
        <el-button
          type="text"
          @click="toggleExpanded"
          class="ml-2"
        >
          {{ isExpanded ? '收起' : '展开' }}
          <Icon :icon="isExpanded ? 'ep:arrow-up' : 'ep:arrow-down'" class="ml-1" />
        </el-button>
      </el-form-item>
    </el-form>
  </ContentWrap>

  <!-- 列表 -->
  <ContentWrap>
    <el-table
      v-loading="loading"
      :data="flattenedList"
      :stripe="true"
      border
      :show-overflow-tooltip="true"
      highlight-current-row
      @current-change="handleCurrentChange"
      @selection-change="handleSelectionChange"
      :span-method="objectSpanMethod"
      :max-height="600"
      style="width: 100%"
      show-summary
      :summary-method="getSummaries"
    >
      <el-table-column type="selection" width="60" fixed="left" />
      <el-table-column label="订单编号" align="left" width="200px" fixed="left" prop="orderNo">
        <template #default="scope">
          <div class="order-no-container">
            <div class="order-no-cell">
              <div class="order-no-content">
                <el-link
                  type="primary"
                  @click="goToDetail(scope.row.id)"
                  v-hasPermi="['purchase:order:query']"
                >
                  {{ scope.row.orderNo }}
                </el-link>
              </div>
              <el-button
                link
                type="info"
                @click="copyOrderNo(scope.row.orderNo)"
                class="copy-btn copy-btn-fixed"
                size="small"
              >
                <Icon icon="ep:copy-document" :size="12"/>
              </el-button>
            </div>
            <div class="status-tags" v-if="scope.row.orderNo">
              <dict-tag
                v-if="scope.row.status"
                :type="DICT_TYPE.PURCHASE_ORDER_STATUS"
                :value="scope.row.status"
                class="status-tag"
              />
              <dict-tag
                v-if="scope.row.approvalStatus"
                :type="DICT_TYPE.APPROVE_STATUS"
                :value="scope.row.approvalStatus"
                class="status-tag"
              />
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="供应商" align="left" prop="supplierName" width="150px" fixed="left"/>

      <!-- 明细信息列 - 按重要性排序 -->
      <el-table-column label="物料名称" align="left" prop="detail.materialName" width="180px" fixed="left">
        <template #default="scope">
         {{ scope.row.detail.materialCode + ' ' + scope.row.detail?.materialName}}
        </template>
      </el-table-column>
      <el-table-column label="物料规格" align="left" prop="detail.spec" width="130px"/>
      <el-table-column label="物料数量" align="right" prop="detail.quantity" width="140px">
        <template #default="scope">
          {{ quantityTableFormatter(null, null, scope.row.detail.quantity, null) }}{{ getUnitName(scope.row.detail?.unit) || scope.row.detail?.unit || '' }}
        </template>
      </el-table-column>

      <el-table-column label="物料单价" align="right" prop="detail.unitPrice" width="120px" :formatter="amountTableFormatter"/>
      <el-table-column label="物料金额" align="right" prop="detail.amount" width="120px" :formatter="amountTableFormatter"/>
      <el-table-column label="含税单价" align="right" prop="detail.unitTaxPrice" width="110px" :formatter="amountTableFormatter"/>
      <el-table-column label="价税合计" align="right" prop="detail.totalAmount" width="110px" :formatter="amountTableFormatter"/>
      <el-table-column label="税率" align="right" prop="detail.tax" width="80px"/>
      <el-table-column label="税额" align="right" prop="detail.taxAmount" width="110px" :formatter="amountTableFormatter"/>
      <el-table-column label="已接收数量" align="right" prop="detail.receivedQuantity" width="110px" :formatter="quantityTableFormatter"/>
      <el-table-column label="批号" align="left" prop="detail.batchNo" width="100px"/>

      <!-- 重要的主订单业务信息 -->
      <el-table-column label="订单日期" align="center" prop="orderDate" width="130px" />
      <el-table-column label="交货日期" align="center" prop="deliveryDate" width="130px" />
      
      <el-table-column label="订单总金额" align="right" prop="totalAmount" width="130px" :formatter="amountTableFormatter"/>
      <el-table-column label="入库" align="center" prop="isInStock" width="80px">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.COMMON_YES_NO" :value="Number(scope.row.isInStock)" />
        </template>
      </el-table-column>


      <!-- 其他明细信息 -->
      <el-table-column label="入库数量" align="right" prop="detail.inStockQuantity" width="100px" :formatter="quantityTableFormatter"/>
<!--      <el-table-column label="辅助单位" align="center" width="100px">
        <template #default="scope">
          {{ getUnitName(scope.row.detail?.auxilaryUnit) || scope.row.detail?.auxilaryUnit || '' }}
        </template>
      </el-table-column>
      <el-table-column label="辅助单位数量" align="right" prop="detail.auxilaryQuantity" width="120px" :formatter="quantityTableFormatter"/>
      <el-table-column label="辅助入库数量" align="right" prop="detail.inAuxilaryQuantity" width="120px" :formatter="quantityTableFormatter"/>-->
      <el-table-column label="在途数量" align="right" prop="detail.inTransitQuantity" width="100px" :formatter="quantityTableFormatter"/>
<!--      <el-table-column label="关联数量" align="right" prop="detail.relateQuantity" width="100px" :formatter="quantityTableFormatter"/>-->
      <el-table-column label="合同单号" align="left" prop="detail.contractNo" width="120px">
        <template #default="scope">
          <div class="order-no-cell" v-if="scope.row.detail.contractNo">
            <div class="order-no-content">
              <span>{{ scope.row.detail.contractNo }}</span>
            </div>
            <el-button
              link
              type="info"
              @click="copyOrderNo(scope.row.detail.contractNo)"
              class="copy-btn copy-btn-fixed"
              size="small"
            >
              <Icon icon="ep:copy-document" :size="12"/>
            </el-button>
          </div>
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column label="开票数量" align="right" prop="detail.invoiceQuantity" width="100px" :formatter="quantityTableFormatter"/>
<!--      <el-table-column label="辅助开票数量" align="right" prop="detail.invoiceAuxilaryQuantity" width="120px" :formatter="quantityTableFormatter"/>-->
      <el-table-column label="需求来源类型" align="center" width="120px">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.PURCHASE_REQ_SOURCE" :value="scope.row.detail?.sourceType" />
        </template>
      </el-table-column>
      <el-table-column label="来源单号" align="left" prop="detail.sourceNo" width="120px">
        <template #default="scope">
          <div class="order-no-cell" v-if="scope.row.detail.sourceNo">
            <div class="order-no-content">
              <span>{{ scope.row.detail.sourceNo }}</span>
            </div>
            <el-button
              link
              type="info"
              @click="copyOrderNo(scope.row.detail.sourceNo)"
              class="copy-btn copy-btn-fixed"
              size="small"
            >
              <Icon icon="ep:copy-document" :size="12"/>
            </el-button>
          </div>
          <span v-else>-</span>
        </template>
      </el-table-column>
      <!-- <el-table-column label="跟进状态" align="center" width="120px">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.PURCHASE_PROGRESS_STATUS" :value="scope.row.detail?.progressStatus" />
        </template>
      </el-table-column> -->
      <el-table-column
        label="接收日期"
        align="center"
        prop="detail.receivedDate"
        :formatter="dateFormatter"
        width="100px"
      />
      <el-table-column label="明细备注" align="left" prop="detail.remark" width="120px"/>
      <el-table-column
        label="明细创建时间"
        align="center"
        prop="detail.createTime"
        :formatter="dateFormatter"
        width="180px"
      />

      <!-- 其他主订单信息 -->
      <el-table-column label="备注" align="left" prop="remark" width="120px"/>
      <el-table-column
        label="创建时间"
        align="center"
        prop="createTime"
        :formatter="dateFormatter"
        width="180px"
      />
      <!-- <el-table-column label="审批单号" align="left" prop="approveNo" width="120px"/> -->
      <!-- <el-table-column
        label="审批时间"
        align="center"
        prop="approvalTime"
        :formatter="dateFormatter"
        width="180px"
      /> -->
      <el-table-column label="操作" align="center" min-width="160px" fixed="right" prop="operation">
        <template #default="scope">
          <el-button
            link
            type="primary"
            @click="openFollowForm('update', scope.row.id)"
            v-hasPermi="['purchase:order:follow']"
          >
            物流
          </el-button>
          <el-button
            link
            type="primary"
            @click="openInForm('create', scope.row.id)"
            v-hasPermi="['purchase:order:in']"
            v-if="scope.row.approvalStatus >= '3'"
          >
            入库
          </el-button>
          <el-button
            link
            type="primary"
            @click="openInspectionForm(scope.row)"
            v-hasPermi="['quality:inspection:create']"
            v-if="scope.row.approvalStatus >= '3'"
          >
            质检
          </el-button>
          <!-- 编辑按钮 - 需要编辑权限 -->
          <el-button
            link
            type="primary"
            @click="openForm('update', scope.row.id)"
            v-hasPermi="['purchase:order:update']"
            v-if="scope.row.approvalStatus !== '3'"
          >
            编辑
          </el-button>
          <el-button
            link
            type="danger"
            @click="handleDelete(scope.row.id)"
            v-hasPermi="['purchase:order:delete']"
            v-if="scope.row.approvalStatus !== '3'"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <Pagination
      :total="total"
      v-model:page="queryParams.pageNo"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
  </ContentWrap>

  <!-- 表单弹窗：添加/修改 -->
  <OrderForm ref="formRef" @success="getList" />
  <PurchaseReceiptForm ref="purchaseReceiptFormRef" @success="getList" />
  <ApproveInfoForm ref="approveInfoFormRef" @success="getList" :biz-id="currentRow?.id" biz-type="purchase_order" :biz-no="currentRow?.orderNo"/>

  <!-- 物流信息弹窗 -->
  <LogisticsInfoForm ref="logisticsFormRef" @success="getList" />

  <!-- 质检表单 -->
  <InspectionForm ref="inspectionFormRef" @success="getList" />

</template>

<script setup lang="ts">
import { getStrDictOptions, DICT_TYPE,getDictLabel } from '@/utils/dict'
import { dateFormatter } from '@/utils/formatTime'
import download from '@/utils/download'
import { OrderApi, OrderVO } from '@/api/scm/purchase/order'
import { getRemoteUnit } from '@/utils/commonBiz'
import OrderForm from './OrderForm.vue'
import PurchaseReceiptForm from '@/views/scm/inventory/purchasereceipt/PurchaseReceiptForm.vue'
import ApproveInfoForm from '../../base/approveinfo/ApproveInfoForm.vue'
import LogisticsInfoForm from '@/views/scm/inventory/logisticsinfo/LogisticsInfoForm.vue'
import InspectionForm from '@/views/scm/quality/inspection/InspectionForm.vue'

import { amountTableFormatter, quantityTableFormatter, formatAmount, formatQuantity } from '@/utils/formatter'
import { useClipboard } from '@vueuse/core'

/** 采购订单 列表 */
defineOptions({ name: 'Order' })

const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化
const router = useRouter() // 路由器
const { copy } = useClipboard() // 复制功能

/** 复制订单单号 */
const copyOrderNo = async (orderNo: string) => {
  try {
    await copy(orderNo)
    message.success('单号复制成功')
  } catch (error) {
    message.error('复制失败')
  }
}

const loading = ref(true) // 列表的加载中
const list = ref<(OrderVO & { orderDetails?: any[], details?: any[] })[]>([]) // 列表的数据
const total = ref(0) // 列表的总页数
const isExpanded = ref(false) // 搜索表单展开状态
const unitMap = ref<Map<number, string>>(new Map()) // 单位ID到名称的映射
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  supplierName: undefined,
  orderNo: undefined,
  orderDate: [],
  deliveryDate: [],
  createTime: [],
  isInStock: undefined,
  approveNo: undefined,
  approvalStatus: undefined,
  materialName: undefined,
  materialCode: undefined,
  detail: true
})
const queryFormRef = ref() // 搜索的表单
const exportLoading = ref(false) // 导出的加载中
const selectedRows = ref<OrderVO[]>([]) // 选中的行
const approveInfoFormRef = ref()
/** 加载单位数据 */
const loadUnits = async () => {
  try {
    // 批量获取所有单位信息
    const units = await getRemoteUnit()

    if (!units || units.length === 0) {
      return
    }

    // 建立单位映射
    units.forEach((unit: any) => {
      if (unit && unit.id && unit.name) {
        unitMap.value.set(unit.id, unit.name)
      }
    })
  } catch (error) {
    console.error('加载单位数据失败:', error)
  }
}

/** 获取单位名称 */
const getUnitName = (unitId: number | string) => {
  if (!unitId) return ''
  const id = typeof unitId === 'string' ? parseInt(unitId) : unitId
  const unitName = unitMap.value.get(id)
  return unitName || unitId.toString()
}


/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    // 准备查询参数，转换字符串为布尔值
    const params = { ...queryParams } as any
    if (typeof params.isInStock === 'string') {
      params.isInStock = params.isInStock === '1'
    }

    const data = await OrderApi.getOrderPage(params)
    list.value = data.list
    total.value = data.total

    // 直接使用列表中的 details 字段作为明细数据
    list.value.forEach(order => {
      // 将 details 字段赋值给 orderDetails，保持原有的属性名
      order.orderDetails = order.details || []
    })
  } finally {
    loading.value = false
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  handleQuery()
}

/** 切换展开/收起状态 */
const toggleExpanded = () => {
  isExpanded.value = !isExpanded.value
}



/** 选中行操作 */
const currentRow = ref<OrderVO>({} as OrderVO) // 选中行
const handleCurrentChange = (row: any) => {
  currentRow.value = row
}

/** 选择变化处理 */
const handleSelectionChange = (selection: OrderVO[]) => {
  selectedRows.value = selection
}

// 定义合并行数存储数组
const spanArr = ref<number[]>([])

const flattenedList = computed(() => {
  const result: any[] = []
  spanArr.value = [] // 每次重新计算时清空旧数据

  list.value.forEach(order => {
    const details = order.orderDetails?.length ? order.orderDetails : [{}] // 确保无明细时也有占位行
    const detailCount = details.length

    details.forEach((detail: any, index: number) => {
      result.push({ ...order, detail })
      // 主信息列只在第一个明细行合并，合并行数=明细数量
      spanArr.value.push(index === 0 ? detailCount : 0)
    })
  })

  return result
})

// 需要合并的主信息列（必须与el-table-column的prop严格匹配）
const mergeFields = [
  'orderNo', 'supplierName', 'orderDate', 'deliveryDate', 'status', 'totalAmount',
  'isInStock', 'approvalStatus', 'remark', 'createTime', 'approveNo',
  'approvalTime', 'operation'
]

const objectSpanMethod = ({ column, rowIndex }: { row: any, column: any, rowIndex: number }) => {
  // 处理选择列和主信息列合并
  if (column.type === 'selection' || mergeFields.includes(column.property)) {
    const span = spanArr.value[rowIndex]
    return {
      rowspan: span, // 合并行数
      colspan: span > 0 ? 1 : 0 // 0表示隐藏单元格
    }
  }
  // 明细列不合并
  return { rowspan: 1, colspan: 1 }
}

/** 表格汇总方法 */
const getSummaries = (param: any) => {
  const { columns, data } = param
  const sums: string[] = []

  columns.forEach((column: any, index: number) => {
    if (index === 0) {
      sums[index] = '合计'
      return
    }

    // 需要汇总的数量字段
    const quantityFields = [
      'detail.quantity', 'detail.receivedQuantity', 'detail.inStockQuantity',
      'detail.auxilaryQuantity', 'detail.inAuxilaryQuantity', 'detail.inTransitQuantity',
      'detail.relateQuantity', 'detail.invoiceQuantity', 'detail.invoiceAuxilaryQuantity'
    ]

    // 需要汇总的金额字段
    const amountFields = [
      'detail.unitPrice', 'detail.amount', 'detail.unitTaxPrice',
      'detail.totalAmount', 'detail.taxAmount', 'totalAmount'
    ]

    if (quantityFields.includes(column.property)) {
      // 数量字段汇总
      const values = data.map((item: any) => {
        const value = column.property.split('.').reduce((obj: any, key: string) => obj?.[key], item)
        return Number(value) || 0
      })
      const sum = values.reduce((prev: number, curr: number) => prev + curr, 0)
      sums[index] = formatQuantity(sum)
    } else if (amountFields.includes(column.property)) {
      // 金额字段汇总
      const values = data.map((item: any) => {
        const value = column.property.split('.').reduce((obj: any, key: string) => obj?.[key], item)
        return Number(value) || 0
      })
      const sum = values.reduce((prev: number, curr: number) => prev + curr, 0)
      sums[index] = formatAmount(sum)
    } else {
      // 其他字段不汇总
      sums[index] = ''
    }
  })

  return sums
}

/** 添加/修改操作 */
const formRef = ref()
/** 采购收货单操作 */
const purchaseReceiptFormRef = ref()
/** 物流管理弹窗 */
const logisticsFormRef = ref()
/** 质检表单引用 */
const inspectionFormRef = ref()

const openForm = (type: string, id?: number) => {
  formRef.value.open(type, id)
}

/** 跳转到详情页面 */
const goToDetail = (id: number) => {
  router.push(`/scm/purchase/order/detail/${id}`)
}

/** 导出单个订单 */
const exportSingleOrder = async (id: number) => {
  try {
    exportLoading.value = true
    const data = await OrderApi.exportOrder({ id })
    download.excel(data, `采购订单-${id}.xls`)
  } catch (error) {
    console.error('导出失败:', error)
    message.error('导出失败')
  } finally {
    exportLoading.value = false
  }
}
const openInForm = (type: string, id?: number) => {
  // 根据ID找到对应的采购订单数据
  const purchaseOrder = flattenedList.value.find(item => item.id === id)
  if (purchaseOrder) {
    // 传递采购订单数据到采购入库表单
    purchaseReceiptFormRef.value.open(type, null, purchaseOrder)
  } else {
    // 如果没找到数据，仍然打开表单但不自动填充
    purchaseReceiptFormRef.value.open(type, null)
  }
}

/** 根据物料类型设置默认收货仓库 */
const getDefaultArrivalWarehouse = (orderDetailData: any[]) => {
  if (!orderDetailData || orderDetailData.length === 0) {
    return 6256 // 默认原材料仓库ID
  }

  // 检查是否有包装材料
  const hasPackagingMaterial = orderDetailData.some((detail: any) => {
    const materialCode = detail.materialCode || ''
    const materialType = detail.materialType || detail.type || ''

    // 物料编码以4开头，或物料类型为4，则为包装材料
    return materialCode.startsWith('4') || materialType === '4'
  })

  // 如果有包装材料，选择包装材料仓库，否则选择原材料仓库
  return hasPackagingMaterial ? 6260 : 6256
}

/** 跟进操作 */
const openFollowForm = async (_type: string, id?: number) => {
  if (id) {
    try {
      // 查询是否已有物流信息
      const { LogisticsInfoApi } = await import('@/api/scm/inventory/logisticsinfo')
      const logisticsData = await LogisticsInfoApi.getLogisticsInfoPage({
        orderId: id,
        orderType: 'purchase_order',
        pageNo: 1,
        pageSize: 1
      })

      if (logisticsData.list && logisticsData.list.length > 0) {
        // 已有物流信息，编辑
        logisticsFormRef.value.open('update', logisticsData.list[0].id)
      } else {
        // 没有物流信息，新增 - 获取订单详细信息和明细
        const orderInfo = list.value.find(item => item.id === id)
        // 获取订单明细信息
        const orderDetailData = await OrderApi.getOrderDetailListByOrderId(id)

        // 根据物料类型设置默认收货仓库
        const defaultArrivalWarehouse = getDefaultArrivalWarehouse(orderDetailData)

        // 将订单明细转换为物流明细格式
        const logisticsDetails = orderDetailData?.map((detail: any) => ({
          id: undefined, // 新增记录，ID为空
          logisticsId: undefined,
          logisticsNo: undefined,
          sourceId: detail.id, // 采购订单明细ID
          sourceNo: detail.orderNo, // 采购订单号
          sourceDetailId: detail.id, // 采购订单明细ID
          materialId: detail.materialId,
          materialName: detail.materialName,
          materialCode: detail.materialCode || '',
          spec: detail.spec || '',
          quantity: detail.quantity,
          unit: detail.unit,
          pieces: undefined,
          remark: detail.remark || ''
        })) || []

        // 准备初始数据，包含订单信息、默认收货仓库和明细信息
        let initialData = {
          orderType: 'purchase_order',
          orderId: id,
          orderNo: orderInfo?.orderNo || '',
          arrivalWarehouse: defaultArrivalWarehouse, // 设置默认收货仓库
          logisticsDetails: logisticsDetails // 添加明细信息
        }

        logisticsFormRef.value.open('create', undefined, initialData)
      }
    } catch (error) {
      console.error('查询物流信息失败:', error)
      message.error('查询物流信息失败')
    }
  }
}

/** 打开质检表单 */
const openInspectionForm = async (row: any) => {
  try {

    // 获取采购订单明细数据
    let detailsData = []
    try {
      // 获取订单明细信息
      detailsData = await OrderApi.getOrderDetailListByOrderId(row.id)
    } catch (error) {
      console.error('获取订单明细失败:', error)
      detailsData = []
    }

    // 处理明细数据，转换为质检表单期望的格式
    const processedDetails = await Promise.all(detailsData.map(async (detail: any) => {
      const processedDetail = { ...detail }

      // 如果有unit字段但没有unitName，则获取单位名称
      if (processedDetail.unit && !processedDetail.unitName) {
        try {
          const unitId = typeof processedDetail.unit === 'string' ? parseInt(processedDetail.unit) : processedDetail.unit
          if (!isNaN(unitId)) {
            const unitName = getUnitName(unitId)
            if (unitName) {
              processedDetail.unitName = unitName
            }
          }
        } catch (error) {
          console.error('获取单位信息失败:', error)
        }
      }

      // 转换为质检表单期望的字段格式
      processedDetail.materialId = detail.materialId
      processedDetail.materialName = detail.materialName
      processedDetail.materialCode = detail.materialCode || ''
      processedDetail.materialSpec = detail.spec || ''
      // 设置质检数量 - 使用采购订单的数量作为质检数量
      processedDetail.fulfilledQuantity = detail.quantity || 0
      processedDetail.plannedQuantity = detail.quantity || 0

      return processedDetail
    }))

    // 构造质检表单需要的数据
    const inspectionData = {
      id: row.id,
      orderNo: row.orderNo,
      sourceType: 'purchase_order', // 来源类型为采购订单
      sourceNo: row.orderNo,
      objectName: row.supplierName,
      date: row.orderDate,
      details: processedDetails,
      // 添加其他需要的字段
      supplierId: row.supplierId,
      supplierName: row.supplierName,
      remark: row.remark
    }

    // 打开质检表单并传入数据
    inspectionFormRef.value.open('create', undefined, inspectionData)
  } catch (error) {
    console.error('获取采购订单详情失败:', error)
    message.error('获取采购订单详情失败，请重试')
  }
}

/** 删除按钮操作 */
const handleDelete = async (id: number) => {
  try {
    // 删除的二次确认
    await message.delConfirm()
    // 发起删除
    await OrderApi.deleteOrder(id)
    message.success(t('common.delSuccess'))
    // 刷新列表
    await getList()
  } catch {}
}

/** 导出按钮操作 */
const handleExport = async () => {
  try {
    // 导出的二次确认
    await message.exportConfirm()
    // 发起导出
    exportLoading.value = true
    // 准备导出参数，转换字符串为布尔值
    const params = { ...queryParams } as any
    if (typeof params.isInStock === 'string') {
      params.isInStock = params.isInStock === '1'
    }
    const data = await OrderApi.exportOrder(params)
    download.excel(data, '采购订单.xls')
  } catch {
  } finally {
    exportLoading.value = false
  }
}

/** 审核按钮操作 */
const handleApproval = async () => {
  console.log('handleApproval', selectedRows.value)
  if(!selectedRows.value || selectedRows.value.length === 0) {
    message.error('请选择要审核的订单！')
    return
  }

  // 如果选中了多个订单，提示用户
  if(selectedRows.value.length > 1) {
    message.warning('当前只支持单个订单审核，请选择一个订单进行审核')
    return
  }

  // 设置当前行为选中的第一个订单
  const selectedOrder = selectedRows.value[0]
  currentRow.value = selectedOrder

  // 发起审批，传入业务数据
  approveInfoFormRef.value.open('approve', undefined, {
    bizId: selectedOrder?.id,
    bizNo: selectedOrder?.orderNo,
    bizType: 'purchase_order'
  })
}

/** 初始化 **/
onMounted(async () => {
  // 先加载单位数据
  await loadUnits()
  // 再加载列表数据
  getList()
})
</script>

<style scoped lang="scss">
/* 订单编号容器样式 */
.order-no-container {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 4px;
  padding: 4px 0;
}

/* 状态标签容器样式 */
.status-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
  align-items: center;
}

/* 订单号行布局 */
.order-no-cell {
  display: flex;
  align-items: center;
  width: 100%;
  min-height: 20px;
  gap: 5px; /* 文字和图标之间保持一点点间隔 */
}

.order-no-content {
  flex: 0 1 auto; /* 改为自适应宽度，不占满剩余空间 */
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: calc(100% - 16px); /* 为图标预留空间 */
}

/* 复制按钮样式 */
.copy-btn {
  padding: 2px !important;
  height: 20px !important;
  min-height: 20px !important;
  width: 20px !important;
  font-size: 12px;
  opacity: 0.6;
  transition: opacity 0.2s;
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: center;
}



.copy-btn:hover {
  opacity: 1;
}

/* 状态标签样式 */
.status-tag :deep(.el-tag) {
  font-size: 9px !important;
  padding: 1px 4px !important;
  height: 16px !important;
  line-height: 14px !important;
  border-radius: 6px !important;
  font-weight: 500 !important;
  flex-shrink: 0;
}
</style>
