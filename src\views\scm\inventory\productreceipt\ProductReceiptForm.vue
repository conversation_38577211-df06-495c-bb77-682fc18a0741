<template>
  <Dialog :title="dialogTitle" v-model="dialogVisible" :width="1000">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="auto"
      v-loading="formLoading"
    >
      <el-row :gutter="20">
        <el-col :span="8">
          <el-form-item label="单号" prop="orderNo">
            <el-input v-model="formData.orderNo" placeholder="保存时自动生成" disabled/>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="业务类型" prop="bizType">
            <el-select v-model="formData.bizType" placeholder="请选择业务类型" disabled>
              <el-option
                v-for="item in scm_biz_type"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="来源类型" prop="sourceType">
            <el-select v-model="formData.sourceType" placeholder="请选择来源类型" disabled>
              <el-option 
              v-for="item in product_source_type" 
              :key="item.value" 
              :label="item.label" 
              :value="item.value" 
              />
            </el-select>
          </el-form-item>
        </el-col>
        <!-- <el-col :span="8">
          <el-form-item label="来源单ID" prop="sourceId">
            <el-input v-model="formData.sourceId" placeholder="请输入来源单ID" />
          </el-form-item>
        </el-col> -->
        <!-- <el-col :span="8">
          <el-form-item label="来源单" prop="sourceNo">
            <el-select
              v-model="formData.sourceNo"
              placeholder="请选择来源单"
              filterable
              clearable
              @change="handleWorkOrderChange"
            >
              <el-option
                v-for="workOrder in workOrderList"
                :key="workOrder.id"
                :label="`${workOrder.workNo} - ${workOrder.productName}`"
                :value="workOrder.workNo"
              >
                <div style="display: flex; justify-content: space-between; align-items: center;">
                  <span style="font-weight: 500;">{{ workOrder.workNo }}</span>
                  <span style="color: #909399; font-size: 12px;">{{ workOrder.productName }}</span>
                </div>
                <div style="color: #c0c4cc; font-size: 11px; margin-top: 2px;">
                  客户: {{ workOrder.customerName || '未知' }} | 状态: {{ getWorkOrderStatusText(workOrder.status) }}
                </div>
              </el-option>
            </el-select>
          </el-form-item>
        </el-col> -->
        <!-- <el-col :span="8">
          <el-form-item label="交易对象ID" prop="objectId">
            <el-input v-model="formData.objectId" placeholder="请输入交易对象ID" />
          </el-form-item>
        </el-col> -->
<!--        <el-col :span="8">
          <el-form-item label="交易对象名称" prop="objectName">
            <el-input v-model="formData.objectName" placeholder="请输入交易对象名称" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="交易对象订单号" prop="objectOrderNo">
            <el-input v-model="formData.objectOrderNo" placeholder="请输入交易对象订单号" />
          </el-form-item>
        </el-col>-->
        <el-col :span="8">
          <el-form-item label="交易日期" prop="date">
            <el-date-picker
              v-model="formData.date"
              type="date"
              value-format="x"
              placeholder="选择交易日期"
            />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="仓库名称" prop="warehouseId">
            <el-tree-select v-model="formData.warehouseId" :data="warehouseList" placeholder="请选择仓库"/>
          </el-form-item>
        </el-col>
        <!-- <el-col :span="8">
          <el-form-item label="科目ID" prop="accountId">
            <el-input v-model="formData.accountId" placeholder="请输入科目ID" />
          </el-form-item>
        </el-col> -->
        <el-col :span="8">
          <el-form-item label="摘要" prop="note">
            <el-input v-model="formData.note" placeholder="请输入摘要" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="备注" prop="remark">
            <el-input v-model="formData.remark" placeholder="请输入备注" />
          </el-form-item>
        </el-col>
        <!-- <el-col :span="8">
          <el-form-item label="审批状态" prop="approveStatus">
            <el-radio-group v-model="formData.approveStatus">
              <el-radio 
              v-for="item in approve_status" 
              :key="item.value" 
              :label="item.label" 
              :value="item.value" 
              />
            </el-radio-group>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="审批单号" prop="approveNo">
            <el-input v-model="formData.approveNo" placeholder="请输入审批单号" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="审批人ID" prop="approverId">
            <el-input v-model="formData.approverId" placeholder="请输入审批人ID" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="审批人" prop="approverName">
            <el-input v-model="formData.approverName" placeholder="请输入审批人" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="审批时间" prop="approveDate">
            <el-date-picker
              v-model="formData.approveDate"
              type="date"
              value-format="x"
              placeholder="选择审批时间"
            />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="部门ID" prop="deptId">
            <el-input v-model="formData.deptId" placeholder="请输入部门ID" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="业务员ID" prop="empId">
            <el-input v-model="formData.empId" placeholder="请输入业务员ID" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="管理员ID" prop="managerId">
            <el-input v-model="formData.managerId" placeholder="请输入管理员ID" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="管理员1ID" prop="manger1Id">
            <el-input v-model="formData.manger1Id" placeholder="请输入管理员1ID" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="记账ID" prop="accountantId">
            <el-input v-model="formData.accountantId" placeholder="请输入记账ID" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="检验员ID" prop="checkerId">
            <el-input v-model="formData.checkerId" placeholder="请输入检验员ID" />
          </el-form-item>
        </el-col> -->
      </el-row>
    </el-form>
    <!-- 子表的表单 -->
    <el-tabs v-model="subTabsName">
      <el-tab-pane label="产品入库明细" name="productReceiptDetail">
        <ProductReceiptDetailForm ref="productReceiptDetailFormRef" :biz-order-id="formData.id" :warehouse-id="formData.warehouseId"/>
      </el-tab-pane>
    </el-tabs>
    <template #footer>
      <el-button @click="submitForm" type="primary" :disabled="formLoading">确 定</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>
</template>
<script setup lang="ts">
import { ProductReceiptApi, ProductReceiptVO } from '@/api/scm/inventory/productreceipt'
import ProductReceiptDetailForm from './components/ProductReceiptDetailForm.vue'
import { DICT_TYPE,getStrDictOptions } from '@/utils/dict'
import { getWarehouse } from '@/utils/commonBiz'
import { WorkOrderApi, WorkOrderVO } from '@/api/scm/mfg/workorder'
import { handleTree } from '@/utils/tree'
/** 产品入库 表单 */
defineOptions({ name: 'ProductReceiptForm' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型：create - 新增；update - 修改
const formData = ref({
  id: undefined as number | undefined,
  orderNo: undefined as string | undefined,
  bizType: undefined as string | undefined,
  sourceType: undefined as string | undefined,
  sourceId: undefined as number | undefined,
  sourceNo: undefined as string | undefined,
  objectId: undefined as number | undefined,
  objectName: undefined as string | undefined,
  objectOrderNo: undefined as string | undefined,
  date: undefined as Date | undefined,
  warehouseId: undefined as number | undefined,
  accountId: undefined as number | undefined,
  note: undefined as string | undefined,
  remark: undefined as string | undefined,
  approveStatus: undefined as string | undefined,
  approveNo: undefined as string | undefined,
  approverId: undefined as number | undefined,
  approverName: undefined as string | undefined,
  approveDate: undefined as Date | undefined,
  deptId: undefined as number | undefined,
  empId: undefined as number | undefined,
  managerId: undefined as number | undefined,
  manger1Id: undefined as number | undefined,
  accountantId: undefined as number | undefined,
  checkerId: undefined as number | undefined,
})
const formRules = reactive({
})
const formRef = ref() // 表单 Ref
const approve_status = getStrDictOptions(DICT_TYPE.APPROVE_STATUS)
const product_source_type = getStrDictOptions(DICT_TYPE.PRODUCT_SOURCE_TYPE)
const scm_biz_type = getStrDictOptions(DICT_TYPE.SCM_BIZ_TYPE)
const warehouseList = ref<any[]>([])
const workOrderList = ref<WorkOrderVO[]>([])
/** 子表的表单 */
const subTabsName = ref('productReceiptDetail')
const productReceiptDetailFormRef = ref()

// 仓库数据缓存
const warehouseCache = ref<any[]>([])
const warehouseCacheTime = ref<number>(0)
const CACHE_DURATION = 5 * 60 * 1000 // 5分钟缓存

/** 打开弹窗 */
const open = async (type: string, id?: number, workOrderData?: any) => {
  dialogVisible.value = true
  dialogTitle.value = t('action.' + type)
  formType.value = type
  resetForm()
  await initWarehouseList()
  await initWorkOrderList()

  // 如果传入了工单数据，自动填充表单
  if (workOrderData && type === 'create') {
    // 设置来源类型为生产任务单（使用product_source_type字典）
    formData.value.sourceType = 'mfg_work_order'
    // 设置来源单号为工单编号
    formData.value.sourceNo = workOrderData.workNo
    formData.value.sourceId = workOrderData.id
    // 设置业务类型为生产完工入库
    formData.value.bizType = 'product_receipt'
    // 设置入库日期为当前日期
    formData.value.date = new Date()

    // 触发工单变化处理，自动填充相关字段
    handleWorkOrderChange(workOrderData.workNo)

    // 获取并填充工单明细数据
    await loadWorkOrderDetails(workOrderData.id)

    // 如果主表单没有设置仓库，根据工单产品信息自动设置
    if (!formData.value.warehouseId) {
      const defaultWarehouseId = getDefaultWarehouseByMaterial(workOrderData.productCode, workOrderData.productType)
      if (defaultWarehouseId) {
        formData.value.warehouseId = defaultWarehouseId
      }
    }
  }

  // 修改时，设置数据
  if (id) {
    formLoading.value = true
    try {
      formData.value = await ProductReceiptApi.getProductReceipt(id)
    } finally {
      formLoading.value = false
    }
  }
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  await formRef.value.validate()
  // 校验子表单
  try {
    await productReceiptDetailFormRef.value.validate()
  } catch (e) {
    subTabsName.value = 'productReceiptDetail'
    return
  }
  // 提交请求
  formLoading.value = true
  try {
    const data = formData.value as unknown as ProductReceiptVO
    // 拼接子表的数据
    data.productReceiptDetails = productReceiptDetailFormRef.value.getData()
    if (formType.value === 'create') {
      await ProductReceiptApi.createProductReceipt(data)
      message.success(t('common.createSuccess'))
    } else {
      await ProductReceiptApi.updateProductReceipt(data)
      message.success(t('common.updateSuccess'))
    }
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}
const initWarehouseList = async () => {
  // 检查缓存是否有效
  const now = Date.now()
  if (warehouseCache.value.length > 0 && (now - warehouseCacheTime.value) < CACHE_DURATION) {
    warehouseList.value = warehouseCache.value
    return
  }

  try {
    // 使用通用工具函数获取仓库数据
    const res = await getWarehouse()

    const formatTreeData = (list) => {
      return list.map(item => {
        const node:any = {
          id: item.id,
          label: item.name,
          value: item.id,
          parentId: item.parentId
        }

        if (item.children && item.children.length > 0) {
          node.children = formatTreeData(item.children)
        }
        return node
      })
    }

    const warehouseTree = handleTree(res, 'id', 'parentId')
    const formattedData = formatTreeData(warehouseTree)
    const filteredData = formattedData.filter(item => item.label === '产成品仓' || item.label === '半成品仓')

    // 更新缓存
    warehouseCache.value = filteredData
    warehouseCacheTime.value = now
    warehouseList.value = filteredData

    console.log('仓库列表已缓存:', warehouseList.value)
  } catch (error) {
    console.error('获取仓库列表失败:', error)
    warehouseList.value = []
  }
}

//初始化工作订单数据
const initWorkOrderList = async () => {
  try {
    const res = await WorkOrderApi.getWorkOrderPage({
      pageNo: 1,
      pageSize: 100,
      status: '1' // 只获取已审批的工作订单
    })
    workOrderList.value = res.list || []
  } catch (error) {
    console.error('获取工作订单列表失败:', error)
    workOrderList.value = []
  }
}

// 处理工作订单选择变化
const handleWorkOrderChange = (workNo: string) => {
  if (!workNo) {
    // 清空相关字段
    formData.value.objectName = undefined
    formData.value.objectOrderNo = undefined
    return
  }

  // 根据选择的工作订单编号找到对应的工作订单
  const selectedWorkOrder = workOrderList.value.find(order => order.workNo === workNo)
  if (selectedWorkOrder) {
    // 自动填充相关字段
    console.log(selectedWorkOrder)
    formData.value.sourceId = selectedWorkOrder.id
    formData.value.objectName = selectedWorkOrder.customerName || ''
    formData.value.objectOrderNo = selectedWorkOrder.orderNo || ''
  }
}

// 获取工作订单状态文本
const getWorkOrderStatusText = (status: string) => {
  const statusMap: Record<string, string> = {
    '0': '待审批',
    '1': '已审批',
    '2': '生产中',
    '3': '已完成',
    '4': '已取消'
  }
  return statusMap[status] || '未知状态'
}

// 根据物料编码和类型自动设置默认仓库
const getDefaultWarehouseByMaterial = (materialCode: string, materialType: string | number) => {
  // 如果没有物料编码和类型，返回undefined
  if (!materialCode && !materialType) {
    return undefined
  }

  // 根据物料编码判断
  if (materialCode) {
    // materialCode以2开头，设置为6258
    if (materialCode.startsWith('2')) {
      return 6258
    }
    // materialCode以3开头，设置为6259
    if (materialCode.startsWith('3')) {
      return 6259
    }
  }

  // 根据物料类型判断
  if (materialType) {
    const typeStr = materialType.toString()
    // materialType为2时，设置为6258
    if (typeStr === '2') {
      return 6258
    }
    // materialType为3时，设置为6259
    if (typeStr === '3') {
      return 6259
    }
  }

  // 如果都不匹配，返回undefined，让用户手动选择
  return undefined
}

// 加载工单产品数据并自动填充到产品入库明细
const loadWorkOrderDetails = async (workOrderId: number) => {
  try {

    // 获取工单数据（包含产品信息）
    const workOrderData = await WorkOrderApi.getWorkOrder(workOrderId)

    if (workOrderData) {
      // 创建产品入库明细（基于工单的产品信息）
      const productReceiptDetails = [{
        // 序号 - 自动添加
        num: 1,

        // 产品基本信息（从工单获取）
        materialId: workOrderData.productId,
        materialCode: workOrderData.productCode,
        materialName: workOrderData.productName,
        spec: workOrderData.spec || '',

        // 数量信息 - 使用实际生产数量或计划数量
        quantity: workOrderData.actualQuantity || workOrderData.scheduleQuantity || 0,
        standardQuantity: workOrderData.actualQuantity || workOrderData.scheduleQuantity || 0,
        specQuantity: workOrderData.actualPiece || workOrderData.schedulePiece || 0,

        // 计划数量和完成数量
        plannedQuantity: workOrderData.scheduleQuantity || 0,
        fulfilledQuantity: workOrderData.actualQuantity || 0,

        // 单位信息
        unit: workOrderData.productUnit,
        standardUnit: workOrderData.productUnit,

        // 价格信息（产品入库通常不涉及价格，设为0）
        unitPrice: 0,
        amount: 0,
        taxPrice: 0,
        taxAmount: 0,

        // 仓库库位信息（根据物料编码和类型自动设置）
        warehouseId: getDefaultWarehouseByMaterial(workOrderData.productCode, workOrderData.productType),
        locationId: undefined,

        // 其他信息
        remark: workOrderData.actualRemark || workOrderData.remark || '',
        batchNo: workOrderData.actualBatchNo || '',

        // 产品入库特有字段
        invoiceQuantity: 0,
        invoiceAmount: 0,
        standardInvoiceQuantity: 0,
        effictiveDate: new Date(),
        expiryDate: undefined,
        sourceId: workOrderId,
        sourceNo: formData.value.sourceNo
      }]


      // 将数据设置到子表单中
      if (productReceiptDetailFormRef.value && productReceiptDetailFormRef.value.setData) {
        productReceiptDetailFormRef.value.setData(productReceiptDetails)
      } 
    } 
  } catch (error) {
    console.error('加载工单产品数据失败')
  }
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    id: undefined as number | undefined,
    orderNo: undefined as string | undefined,
    bizType: undefined as string | undefined,
    sourceType: undefined as string | undefined,
    sourceId: undefined as number | undefined,
    sourceNo: undefined as string | undefined,
    objectId: undefined as number | undefined,
    objectName: undefined as string | undefined,
    objectOrderNo: undefined as string | undefined,
    date: undefined as Date | undefined,
    warehouseId: undefined as number | undefined,
    accountId: undefined as number | undefined,
    note: undefined as string | undefined,
    remark: undefined as string | undefined,
    approveStatus: undefined as string | undefined,
    approveNo: undefined as string | undefined,
    approverId: undefined as number | undefined,
    approverName: undefined as string | undefined,
    approveDate: undefined as Date | undefined,
    deptId: undefined as number | undefined,
    empId: undefined as number | undefined,
    managerId: undefined as number | undefined,
    manger1Id: undefined as number | undefined,
    accountantId: undefined as number | undefined,
    checkerId: undefined as number | undefined,
  }
  formRef.value?.resetFields()
}
</script>
