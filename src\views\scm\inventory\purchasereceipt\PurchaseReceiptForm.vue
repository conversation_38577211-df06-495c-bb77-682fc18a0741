<template>
  <Dialog :title="dialogTitle" v-model="dialogVisible" width="80%">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="auto"
      v-loading="formLoading"
    >
      <el-row :gutter="20">
        <el-col :span="8">
          <el-form-item label="单号" prop="orderNo">
            <el-input v-model="formData.orderNo" placeholder="保存时自动生成" disabled/>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="业务类型" prop="bizType">
            <el-select v-model="formData.bizType" placeholder="请选择业务类型" disabled>
              <el-option
                v-for="item in inventory_transaction_type"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="来源类型" prop="sourceType">
            <el-select v-model="formData.sourceType" placeholder="请选择来源类型" disabled>
              <el-option 
              v-for="item in scm_biz_type" 
              :key="item.value" 
              :label="item.label" 
              :value="item.value" 
              />
            </el-select>
          </el-form-item>
        </el-col>
        <!-- <el-col :span="8">
          <el-form-item label="来源单ID" prop="sourceId">
            <el-input v-model="formData.sourceId" placeholder="请输入来源单ID" />
          </el-form-item>
        </el-col> -->
        <el-col :span="8">
          <el-form-item label="来源单" prop="sourceNo">
            <ScrollSelect
              v-model="formData.sourceNo"
              :load-method="loadPurchaseOrders"
              label-key="orderNoWithSupplier"
              value-key="orderNo"
              query-key="orderNo"
              :default-value="purchaseOrderDefaultValue"
              :extra-params="purchaseOrderExtraParams"
              @change="onPurchaseOrderChange"
              placeholder="请选择来源单"
              :key="formData.sourceNo"
              disabled
            />
          </el-form-item>
        </el-col>
        <!-- <el-col :span="8">
          <el-form-item label="交易对象ID" prop="objectId">
            <el-input v-model="formData.objectId" placeholder="请输入交易对象ID" />
          </el-form-item>
        </el-col> -->
        <el-col :span="8">
          <el-form-item label="供应商名称" prop="objectName">
            <el-input v-model="formData.objectName" placeholder="请输入交易对象名称" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="供应商订单号" prop="objectOrderNo">
            <el-input v-model="formData.objectOrderNo" placeholder="请输入供应商订单号" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="入库日期" prop="date">
            <el-date-picker
              v-model="formData.date"
              type="date"
              value-format="x"
              placeholder="选择入库日期"
            />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="仓库名称" prop="warehouseId">
            <el-tree-select v-model="formData.warehouseId" :data="warehouseList" placeholder="请选择仓库"/>
          </el-form-item>
        </el-col>
        <!-- <el-col :span="8">
          <el-form-item label="科目ID" prop="accountId">
            <el-input v-model="formData.accountId" placeholder="请输入科目ID" />
          </el-form-item>
        </el-col> -->
        <el-col :span="8">
          <el-form-item label="摘要" prop="note">
            <el-input v-model="formData.note" placeholder="请输入摘要" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="备注" prop="remark">
            <el-input v-model="formData.remark" placeholder="请输入备注" />
          </el-form-item>
        </el-col>
        <!-- <el-col :span="8">
          <el-form-item label="审批状态" prop="approveStatus">
            <el-radio-group v-model="formData.approveStatus">
              <el-radio 
              v-for="item in approve_status" 
              :key="item.value" 
              :label="item.label" 
              :value="item.value" 
              />
            </el-radio-group>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="审批单号" prop="approveNo">
            <el-input v-model="formData.approveNo" placeholder="请输入审批单号" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="审批人ID" prop="approverId">
            <el-input v-model="formData.approverId" placeholder="请输入审批人ID" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="审批人" prop="approverName">
            <el-input v-model="formData.approverName" placeholder="请输入审批人" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="审批时间" prop="approveDate">
            <el-date-picker
              v-model="formData.approveDate"
              type="date"
              value-format="x"
              placeholder="选择审批时间"
            />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="部门ID" prop="deptId">
            <el-input v-model="formData.deptId" placeholder="请输入部门ID" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="业务员ID" prop="empId">
            <el-input v-model="formData.empId" placeholder="请输入业务员ID" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="管理员ID" prop="managerId">
            <el-input v-model="formData.managerId" placeholder="请输入管理员ID" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="管理员1ID" prop="manger1Id">
            <el-input v-model="formData.manger1Id" placeholder="请输入管理员1ID" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="记账ID" prop="accountantId">
            <el-input v-model="formData.accountantId" placeholder="请输入记账ID" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="检验员ID" prop="checkerId">
            <el-input v-model="formData.checkerId" placeholder="请输入检验员ID" />
          </el-form-item>
        </el-col> -->
      </el-row>
    </el-form>
    <!-- 子表的表单 -->
    <el-tabs v-model="subTabsName">
      <el-tab-pane label="采购入库明细" name="purchaseReceiptDetail">
        <PurchaseReceiptDetailForm ref="purchaseReceiptDetailFormRef" :biz-order-id="formData.id" :warehouse-id="formData.warehouseId" :biz-order-no="formData.orderNo"/>
      </el-tab-pane>
    </el-tabs>
    <template #footer>
      <el-button @click="submitForm" type="primary" :disabled="formLoading">确 定</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>
</template>
<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { useI18n } from '@/hooks/web/useI18n'
import { useMessage } from '@/hooks/web/useMessage'
import { PurchaseReceiptApi, PurchaseReceiptVO } from '@/api/scm/inventory/purchasereceipt'
import PurchaseReceiptDetailForm from './components/PurchaseReceiptDetailForm.vue'
import { DICT_TYPE,getStrDictOptions } from '@/utils/dict'
import { WarehouseApi,WarehouseVO } from '@/api/scm/inventory/warehouse'
import { OrderApi } from '@/api/scm/purchase/order'
import { handleTree } from '@/utils/tree'
import ScrollSelect from '@/components/ScrollSelect/index.vue'

/** 采购入库 表单 */
defineOptions({ name: 'PurchaseReceiptForm' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型：create - 新增；update - 修改
const formData = ref({
  id: undefined as number | undefined,
  orderNo: undefined as string | undefined,
  bizType: undefined as string | undefined,
  sourceType: 'purchase_order',
  sourceId: undefined as number | undefined,
  sourceNo: undefined as string | undefined,
  objectId: undefined as number | undefined,
  objectName: undefined as string | undefined,
  objectOrderNo: undefined as string | undefined,
  date: undefined as number | undefined,
  warehouseId: undefined as number | undefined,
  accountId: undefined as number | undefined,
  note: undefined as string | undefined,
  remark: undefined as string | undefined,
  approveStatus: undefined as string | undefined,
  approveNo: undefined as string | undefined,
  approverId: undefined as number | undefined,
  approverName: undefined as string | undefined,
  approveDate: undefined as number | undefined,
  deptId: undefined as number | undefined,
  empId: undefined as number | undefined,
  managerId: undefined as number | undefined,
  manger1Id: undefined as number | undefined,
  accountantId: undefined as number | undefined,
  checkerId: undefined as number | undefined,
})
const formRules = reactive({
  objectName: [{ required: true, message: '供应商名称不能为空', trigger: 'blur' }]
})
const formRef = ref() // 表单 Ref
const scm_biz_type = getStrDictOptions(DICT_TYPE.SCM_BIZ_TYPE)
const inventory_transaction_type = getStrDictOptions(DICT_TYPE.INVENTORY_TRANSACTION_TYPE)
// const approve_status = getStrDictOptions(DICT_TYPE.APPROVE_STATUS)
const warehouseList = ref<any[]>([])
/** 采购订单相关 */
const purchaseOrderDefaultValue = ref({})
const purchaseOrderExtraParams = ref({})
/** 子表的表单 */
const subTabsName = ref('purchaseReceiptDetail')
const purchaseReceiptDetailFormRef = ref()

/** 打开弹窗 */
const open = async (type: string, id?: number, purchaseOrderData?: any) => {
  dialogVisible.value = true
  dialogTitle.value = t('action.' + type)
  formType.value = type
  resetForm()
  await initWarehouseList()

  // 如果传入了采购订单数据，自动填充表单
  if (purchaseOrderData) {
    // 设置业务类型为采购入库
    formData.value.bizType = 'purchase'
    // 设置来源单信息
    formData.value.sourceId = purchaseOrderData.id
    formData.value.sourceNo = purchaseOrderData.orderNo
    // 设置交易对象信息
    formData.value.objectId = purchaseOrderData.supplierId
    formData.value.objectName = purchaseOrderData.supplierName
    // 设置交易日期为当前日期
    formData.value.date = Date.now()

    // 设置采购订单的默认值，用于回显
    purchaseOrderDefaultValue.value = {
      orderNo: purchaseOrderData.orderNo,
      orderNoWithSupplier: `${purchaseOrderData.orderNo} - ${purchaseOrderData.supplierName || '未知供应商'}`
    }

    // 自动加载采购订单明细
    await loadPurchaseOrderDetails(purchaseOrderData.id)
  }

  // 修改时，设置数据
  if (id) {
    formLoading.value = true
    try {
      formData.value = await PurchaseReceiptApi.getPurchaseReceipt(id)

      // 设置采购订单的默认值，用于回显
      if (formData.value.sourceNo) {
        purchaseOrderDefaultValue.value = {
          orderNo: formData.value.sourceNo,
          orderNoWithSupplier: `${formData.value.sourceNo} - ${formData.value.objectName || '未知供应商'}`
        }
      }

      // 修改时，如果没有设置仓库，根据子表物料类型设置默认仓库
      if (!formData.value.warehouseId) {
        // 获取子表数据来判断物料类型
        try {
          const detailList = await PurchaseReceiptApi.getPurchaseReceiptDetailListByBizOrderId(id)
          if (detailList && detailList.length > 0) {
            const defaultWarehouseId = setDefaultWarehouseByMaterialType(detailList)
            formData.value.warehouseId = defaultWarehouseId
          }
        } catch (error) {
          console.warn('获取子表数据失败，使用默认原材料仓库:', error)
          formData.value.warehouseId = 6256 // 默认原材料仓库
        }
      }
    } finally {
      formLoading.value = false
    }
  } else {
    // 新增时，如果没有采购订单数据，设置默认原材料仓库
    if (!purchaseOrderData && !formData.value.warehouseId) {
      formData.value.warehouseId = 6256 // 默认原材料仓库
    }
  }
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  await formRef.value.validate()
  // 校验子表单
  try {
    await purchaseReceiptDetailFormRef.value.validate()
  } catch (e) {
    subTabsName.value = 'purchaseReceiptDetail'
    return
  }
  // 提交请求
  formLoading.value = true
  try {
    const data = formData.value as unknown as PurchaseReceiptVO
    // 拼接子表的数据
    data.purchaseReceiptDetails = purchaseReceiptDetailFormRef.value.getData()
    if (formType.value === 'create') {
      await PurchaseReceiptApi.createPurchaseReceipt(data)
      message.success(t('common.createSuccess'))
    } else {
      await PurchaseReceiptApi.updatePurchaseReceipt(data)
      message.success(t('common.updateSuccess'))
    }
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}

/** 加载采购订单数据 - 适配 ScrollSelect */
const loadPurchaseOrders = async (params: any) => {
  try {
    const { pageNo = 1, pageSize = 20, orderNo, ...extraParams } = params

    const queryParams = {
      pageNo,
      pageSize,
      ...extraParams
    }

    // 如果有搜索关键词，添加到查询参数
    if (orderNo) {
      queryParams.orderNo = orderNo
    }

    const data = await OrderApi.getOrderPage(queryParams)

    // 为每个订单添加组合显示字段
    const processedList = (data.list || []).map((order: any) => ({
      ...order,
      orderNoWithSupplier: `${order.orderNo} - ${order.supplierName || '未知供应商'}`
    }))

    return {
      list: processedList,
      total: data.total || 0
    }
  } catch (error) {
    return {
      list: [],
      total: 0
    }
  }
}

/** 采购订单选择变化 */
const onPurchaseOrderChange = async (orderNo: string, selectedItem: any) => {
  if (!orderNo || !selectedItem) {
    // 清空相关字段
    formData.value.sourceId = undefined
    formData.value.sourceNo = undefined

    // 清空明细数据
    if (purchaseReceiptDetailFormRef.value && purchaseReceiptDetailFormRef.value.setData) {
      purchaseReceiptDetailFormRef.value.setData([])
    }
    return
  }

  // 根据选中的订单填充相关字段
  try {
    formData.value.sourceId = selectedItem.id
    formData.value.sourceNo = selectedItem.orderNo

    // 更新默认值，用于编辑时回显
    purchaseOrderDefaultValue.value = {
      orderNo: selectedItem.orderNo,
      orderNoWithSupplier: selectedItem.orderNoWithSupplier
    }

    // 获取采购订单明细并添加到采购入库明细中
    await loadPurchaseOrderDetails(selectedItem.id)
  } catch (error) {
    // 处理错误但不显示调试信息
  }
}

/** 根据物料类型设置默认仓库 */
const setDefaultWarehouseByMaterialType = (detailList: any[]) => {
  if (!detailList || detailList.length === 0) {
    return 6256 // 默认原材料仓库
  }

  // 检查是否有包装材料
  const hasPackagingMaterial = detailList.some((detail: any) => {
    const materialCode = detail.materialCode || ''
    const materialType = detail.materialType || detail.type || ''

    // 物料编码以4开头，或物料类型为4，则为包装材料
    return materialCode.startsWith('4') || materialType === '4'
  })

  // 如果有包装材料，选择包装材料仓库，否则选择原材料仓库
  return hasPackagingMaterial ? 6260 : 6256
}

/** 加载采购订单明细并添加到采购入库明细中 */
const loadPurchaseOrderDetails = async (orderId: number) => {
  try {
    // 检查OrderApi是否正确导入
    if (!OrderApi || !OrderApi.getOrderDetailListByOrderId) {
      return
    }

    // 确保orderId是数字类型
    const orderIdNumber = Number(orderId)
    if (isNaN(orderIdNumber)) {
      return
    }

    // 获取采购订单明细
    const orderDetails = await OrderApi.getOrderDetailListByOrderId(orderIdNumber)

    // 检查返回的数据结构
    const detailList = Array.isArray(orderDetails) ? orderDetails : (orderDetails?.list || orderDetails?.data || [])

    if (detailList && detailList.length > 0) {
      // 根据物料类型设置默认仓库
      const defaultWarehouseId = setDefaultWarehouseByMaterialType(detailList)
      formData.value.warehouseId = defaultWarehouseId

      // 获取采购订单编号，优先使用formData中的sourceNo，如果没有则使用第一个明细的orderNo
      const purchaseOrderNo = formData.value.sourceNo || detailList[0]?.orderNo || ''

      // 将采购订单明细转换为采购入库明细格式
      const receiptDetails = detailList.map((detail: any, index: number) => ({
        id: undefined, // 新增时ID为空
        num: index + 1, // 序号
        bizOrderId: formData.value.id, // 采购入库单ID
        bizOrderNo: formData.value.orderNo || '', // 采购入库单号（新建时为空，由后端自动生成）
        warehouseId: defaultWarehouseId, // 使用根据物料类型确定的默认仓库ID
        locationId: undefined, // 库位ID
        materialId: detail.materialId, // 物料ID
        materialName: detail.materialName, // 物料名称
        materialCode: detail.materialCode, // 物料编号
        spec:detail.spec,
        unit: detail.unit, // 单位
        unitPrice: detail.unitPrice || 0, // 单价
        amount: detail.amount || 0, // 金额
        remark: detail.remark || '', // 明细备注
        plannedQuantity: detail.quantity || 0, // 应收数量（来自采购订单的数量）
        fulfilledQuantity: detail.quantity || 0, // 实收数量（默认等于应收数量，用户可修改）
        standardPlannedQuantity: detail.standardQuantity || 0, // 基本单位应收数量
        standardFulfilledQuantity: detail.standardQuantity || 0, // 基本单位实收数量
        standardUnit: detail.standardUnit || '', // 基本单位
        taxPrice: detail.taxPrice || 0, // 含税单价
        taxAmount: detail.taxAmount || 0, // 含税金额
        invoiceQuantity: 0, // 开票数量
        invoiceAmount: 0, // 开票金额
        standardInvoiceQuantity: 0, // 开票基本数量
        effictiveDate: undefined, // 生产日期
        expiryDate: undefined, // 失效日期
        note: detail.note || '', // 说明
        sourceId: orderId, // 源单ID（采购订单明细ID）
        sourceDetailId: detail.id, // 源单明细ID
        sourceNo: purchaseOrderNo, // 源单单号（使用采购订单编号）
        sourceType:'purchase_order',
        batchNo: detail.batchNo, // 批号
        costObjectId: undefined, // 成本对象编码
        costObjectName: undefined, // 成本对象名称
        accountingVoucherNumber: undefined, // 记账凭证号
      }))

      // 将明细数据设置到子表单中
      if (purchaseReceiptDetailFormRef.value && purchaseReceiptDetailFormRef.value.setData) {
        purchaseReceiptDetailFormRef.value.setData(receiptDetails)
      }
    } else {
      // 当来源单明细为空时，清空子表单的明细数据
      if (purchaseReceiptDetailFormRef.value && purchaseReceiptDetailFormRef.value.setData) {
        purchaseReceiptDetailFormRef.value.setData([])
      }
    }
  } catch (error: any) {
    // 处理错误但不显示调试信息
  }
}

const initWarehouseList = async () => {
  const res = await WarehouseApi.getWarehouseList({
    pageNo:1,
    pageSize:100
  })
  const formatTreeData = (list: any[]) => {
    return list.map(item => {
      const node:any = {
        id: item.id,
        label: item.name,
        value: item.id,
        parentId: item.parentId
      }

      if (item.children && item.children.length > 0) {
        node.children = formatTreeData(item.children)
      }

      return node
    })
  }
  const warehouseTree = handleTree(res, 'id', 'parentId')
  warehouseList.value = formatTreeData(warehouseTree)
}
/** 重置表单 */
const resetForm = () => {
  formData.value = {
    id: undefined as number | undefined,
    orderNo: undefined as string | undefined,
    bizType: undefined as string | undefined,
    sourceType: 'purchase_order',
    sourceId: undefined as number | undefined,
    sourceNo: undefined as string | undefined,
    objectId: undefined as number | undefined,
    objectName: undefined as string | undefined,
    objectOrderNo: undefined as string | undefined,
    date: undefined as number | undefined,
    warehouseId: undefined as number | undefined,
    accountId: undefined as number | undefined,
    note: undefined as string | undefined,
    remark: undefined as string | undefined,
    approveStatus: undefined as string | undefined,
    approveNo: undefined as string | undefined,
    approverId: undefined as number | undefined,
    approverName: undefined as string | undefined,
    approveDate: undefined as number | undefined,
    deptId: undefined as number | undefined,
    empId: undefined as number | undefined,
    managerId: undefined as number | undefined,
    manger1Id: undefined as number | undefined,
    accountantId: undefined as number | undefined,
    checkerId: undefined as number | undefined,
  }
  // 重置采购订单默认值
  purchaseOrderDefaultValue.value = {}
  formRef.value?.resetFields()
}



/** 初始化 **/
onMounted(() => {
  initWarehouseList()
})
</script>
