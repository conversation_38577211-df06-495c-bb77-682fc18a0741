<template>
  <Dialog :title="dialogTitle" v-model="dialogVisible" width="65%">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="auto"
      v-loading="formLoading"
      :inline="true"
    >
      <el-form-item label="质检单号" prop="inspectionCode">
        <el-input v-model="formData.inspectionCode" placeholder="请输入质检单号(自动生成)" class="!w-240px" disabled/>
      </el-form-item>
      <el-form-item label="来源类型" prop="sourceType">
        <el-select v-model="formData.sourceType" placeholder="请选择来源类型" class="!w-240px">
          <el-option
            v-for="dict in getStrDictOptions(DICT_TYPE.INSPECT_SOURCE_TYPE)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="来源单据编号" prop="sourceCode">
        <el-input v-model="formData.sourceCode" placeholder="来源单据编号" class="!w-240px" readonly/>
      </el-form-item>
      <el-form-item label="关联订单编号" prop="salesOrderCode" v-if="formData.sourceType !== 'purchase_receipt'">
        <el-input v-model="formData.salesOrderCode" placeholder="关联订单编号" class="!w-240px" readonly/>
      </el-form-item>
      <el-form-item label="供应商名称" prop="supplierName" v-if="formData.sourceType === 'purchase_receipt'">
        <el-input v-model="formData.supplierName" placeholder="供应商名称" class="!w-240px" readonly/>
      </el-form-item>
      <el-form-item label="质检人员" prop="inspectorId">
        <el-select v-model="formData.inspectorId" placeholder="请选择质检人员" class="!w-240px" filterable @change="handleInspectorChange">
          <el-option
            v-for="user in userList"
            :key="user.id"
            :label="user.nickname"
            :value="user.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="质检日期" prop="inspectionDate">
        <el-date-picker
          v-model="formData.inspectionDate"
          type="datetime"
          value-format="x"
          placeholder="选择质检日期" class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-select v-model="formData.status" placeholder="请选择状态" class="!w-240px">
          <el-option
            v-for="dict in getIntDictOptions(DICT_TYPE.INSPECT_STATUS)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="质检结果" prop="result">
        <el-select v-model="formData.result" placeholder="请选择质检结果" class="!w-240px">
          <el-option
            v-for="dict in getIntDictOptions(DICT_TYPE.INSPECT_RESULT)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="质检备注" prop="remark">
        <el-input v-model="formData.remark" placeholder="请输入质检备注" class="!w-240px"/>
      </el-form-item>
    </el-form>
    <!-- 子表的表单 -->
    <el-tabs v-model="subTabsName">
      <el-tab-pane label="质检明细" name="inspectionItem">
        <InspectionItemForm ref="inspectionItemFormRef" :inspection-id="formData.id" />
      </el-tab-pane>
    </el-tabs>
    <template #footer>
      <el-button @click="submitForm" type="primary" :disabled="formLoading">确 定</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>
</template>
<script setup lang="ts">
import { nextTick } from 'vue'
import { getIntDictOptions, DICT_TYPE, getStrDictOptions } from '@/utils/dict'
import { InspectionApi } from '@/api/scm/quality/inspection'
import InspectionItemForm from './components/InspectionItemForm.vue'
import { getSimpleUserList, type UserVO } from '@/api/system/user'
import { useUserStore } from '@/store/modules/user'

/** 质检单 表单 */
defineOptions({ name: 'InspectionForm' })

const userStore = useUserStore()
const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型：create - 新增；update - 修改
const formData = ref<any>({
  id: undefined,
  inspectionCode: undefined,
  sourceType: undefined,
  sourceId: undefined,
  sourceCode: undefined,
  salesOrderId: undefined,
  salesOrderCode: undefined,
  supplierName: undefined, // 供应商名称
  inspectorId: userStore.user.id, // 默认当前用户(),
  inspector: userStore.user.nickname,
  inspectionDate: new Date().getTime(),
  status: undefined,
  result: undefined,
  remark: undefined,
})
const formRules = reactive({
  // inspectionCode: [{ required: true, message: '质检单号不能为空', trigger: 'blur' }],
  sourceType: [{ required: true, message: '来源类型不能为空', trigger: 'change' }],
  sourceId: [{ required: true, message: '来源单据ID不能为空', trigger: 'blur' }],
  sourceCode: [{ required: true, message: '来源单据编号不能为空', trigger: 'blur' }],
  inspectionDate: [{ required: true, message: '质检日期不能为空', trigger: 'blur' }],
})
const formRef = ref() // 表单 Ref
const userList = ref<UserVO[]>([]) // 用户列表

/** 子表的表单 */
const subTabsName = ref('inspectionItem')
const inspectionItemFormRef = ref()

/** 打开弹窗 */
const open = async (type: string, id?: number, sourceData?: any) => {
  dialogVisible.value = true
  dialogTitle.value = t('action.' + type)
  formType.value = type
  resetForm()

  // 加载用户列表
  await loadUserList()
  // 修改时，设置数据
  if (id) {
    formLoading.value = true
    try {
      formData.value = await InspectionApi.getInspection(id)
    } finally {
      formLoading.value = false
    }
  }
  // 如果传入了来源数据，预填充表单
  if (sourceData) {
    // 判断来源类型并设置相应的数据
    // 优先判断工单数据（因为工单也可能有orderNo字段，但那是销售订单编号）
    if (sourceData.workNo) {
      // 工单数据
      formData.value.sourceType = 'product_receipt' // 来源类型为产品入库
      formData.value.sourceId = sourceData.id // 来源单据ID（工单ID）
      formData.value.sourceCode = sourceData.workNo // 来源单据编号（工单编号）
      formData.value.salesOrderId = sourceData.orderId // 销售订单ID
      formData.value.salesOrderCode = sourceData.orderNo // 销售订单编号
      formData.value.inspectionDate = new Date().getTime() // 质检日期为当前日期

      // 等待对话框和子组件完全挂载后再设置数据
      await nextTick()
      setTimeout(async () => {
        // 自动填充质检明细数据
        await loadInspectionItems(sourceData)
      }, 300) // 给子组件更多时间完成挂载
    } else if (sourceData.sourceType === 'purchase_order') {
      // 采购订单数据
      formData.value.sourceType = 'purchase_order' // 来源类型为采购订单
      formData.value.sourceId = sourceData.id // 来源单据ID
      formData.value.sourceCode = sourceData.orderNo // 来源单据编号
      formData.value.supplierName = sourceData.supplierName || sourceData.objectName // 供应商名称
      formData.value.inspectionDate = new Date().getTime() // 质检日期为当前日期

      // 自动填充质检明细数据（基于采购订单明细，使用采购入库的处理逻辑）
      await loadInspectionItemsFromPurchaseReceipt(sourceData)
    } else if (sourceData.orderNo && !sourceData.workNo) {
      // 采购入库数据（只有orderNo但没有workNo）
      formData.value.sourceType = 'purchase_receipt' // 来源类型为采购入库
      formData.value.sourceId = sourceData.id // 来源单据ID
      formData.value.sourceCode = sourceData.orderNo // 来源单据编号
      formData.value.supplierName = sourceData.objectName // 供应商名称（采购入库中的交易对象名称）
      formData.value.inspectionDate = new Date().getTime() // 质检日期为当前日期

      // 自动填充质检明细数据（基于采购入库明细）
      await loadInspectionItemsFromPurchaseReceipt(sourceData)
    }
  }
}

// 加载质检明细数据（基于采购入库明细）
const loadInspectionItemsFromPurchaseReceipt = async (purchaseReceiptData: any) => {
  try {
    const inspectionItems: any[] = []

    // 基于采购入库明细创建质检明细
    if (purchaseReceiptData.details && purchaseReceiptData.details.length > 0) {
      purchaseReceiptData.details.forEach((detail: any) => {
        const inspectionItem = {
          id: undefined,
          inspectionId: undefined,
          productId: detail.materialId, // 使用物料ID作为产品ID
          productCode: detail.materialCode || '', // 物料编码
          productName: detail.materialName || '', // 物料名称
          productSpec: detail.materialSpec || '', // 物料规格
          inspectQuantity: detail.fulfilledQuantity || detail.plannedQuantity || 0, // 使用实收数量作为质检数量
          qualifiedQuantity: 0, // 待用户填写
          unqualifiedQuantity: 0, // 待用户填写
          defectCode: '', // 缺陷代码
          defectDesc: '', // 缺陷描述
          disposalMethod: undefined, // 处理方式
          remark: detail.remark || detail.note || '', // 备注
          // 额外信息（虽然质检明细表单中不显示，但可以保存用于后续处理）
          batchNo: detail.batchNo || '', // 批号
          unit: detail.unit || '', // 单位
          unitName: detail.unitName || '', // 单位名称
          unitPrice: detail.unitPrice || 0, // 单价
          effectiveDate: detail.effictiveDate || '', // 生产日期（注意原字段有拼写错误）
          expiryDate: detail.expiryDate || '', // 失效日期
          warehouseId: detail.warehouseId || '', // 仓库ID
          locationId: detail.locationId || '', // 库位ID
        }
        inspectionItems.push(inspectionItem)
      })
    }

    // 设置质检明细数据到子表单
    if (inspectionItemFormRef.value && inspectionItemFormRef.value.setData) {
      inspectionItemFormRef.value.setData(inspectionItems)
    } else {
      // 如果子表单还没有setData方法，延迟设置
      nextTick(() => {
        if (inspectionItemFormRef.value && inspectionItemFormRef.value.setData) {
          inspectionItemFormRef.value.setData(inspectionItems)
        }
      })
    }
  } catch (error) {
    console.error('加载采购入库质检明细数据失败:', error)
  }
}

// 加载质检明细数据（基于工单数据）
const loadInspectionItems = async (workOrderData: any) => {
  try {
    // 基于工单的产品信息创建质检明细
    if (workOrderData.productId) {
      const inspectionItem = {
        id: undefined,
        inspectionId: undefined,
        productId: workOrderData.productId,
        productCode: workOrderData.productCode,
        productName: workOrderData.productName,
        productSpec: workOrderData.spec || '',
        inspectQuantity: workOrderData.actualQuantity || workOrderData.scheduleQuantity || 0,
        qualifiedQuantity: 0, // 待用户填写
        unqualifiedQuantity: 0, // 待用户填写
        defectCode: '',
        defectDesc: '',
        disposalMethod: undefined,
        remark: '',
        unit: workOrderData.unit || '', // 单位
        unitName: workOrderData.unitName || '', // 单位名称
      }

      // 使用多重尝试确保数据设置成功
      const setInspectionData = async () => {
        if (inspectionItemFormRef.value && inspectionItemFormRef.value.setData) {
          await inspectionItemFormRef.value.setData([inspectionItem])
          return true
        }
        return false
      }

      // 立即尝试设置数据
      const success = await setInspectionData()

      if (!success) {
        // 如果立即设置失败，使用nextTick重试
        await nextTick()
        const retrySuccess = await setInspectionData()

        if (!retrySuccess) {
          // 如果还是失败，使用延时重试
          setTimeout(async () => {
            await setInspectionData()
          }, 100)
        }
      }
    }
  } catch (error) {
    console.error('加载质检明细数据失败:', error)
  }
}

defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  await formRef.value.validate()
  // 校验子表单
  try {
    await inspectionItemFormRef.value.validate()
  } catch (e) {
    subTabsName.value = 'inspectionItem'
    return
  }
  // 提交请求
  formLoading.value = true
  try {
    const data = formData.value as any
    // 拼接子表的数据
    data.inspectionItems = inspectionItemFormRef.value.getData()
    if (formType.value === 'create') {
      await InspectionApi.createInspection(data)
      message.success(t('common.createSuccess'))
    } else {
      await InspectionApi.updateInspection(data)
      message.success(t('common.updateSuccess'))
    }
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}

/** 加载用户列表 */
const loadUserList = async () => {
  try {
    userList.value = await getSimpleUserList()
  } catch (error) {
    console.error('加载用户列表失败:', error)
  }
}

/** 处理质检人员选择变化 */
const handleInspectorChange = (inspectorId: number) => {
  if (inspectorId) {
    // 根据选择的用户ID找到对应的用户信息
    const selectedUser = userList.value.find(user => user.id === inspectorId)
    if (selectedUser) {
      formData.value.inspectorId = inspectorId
      formData.value.inspector = selectedUser.nickname
    }
  } else {
    // 清空选择时，同时清空两个字段
    formData.value.inspectorId = undefined
    formData.value.inspector = undefined
  }
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    id: undefined,
    inspectionCode: undefined,
    sourceType: undefined,
    sourceId: undefined,
    sourceCode: undefined,
    salesOrderId: undefined,
    salesOrderCode: undefined,
    supplierName: undefined,
    inspectorId: userStore.user.id, // 默认当前用户(),
    inspector: userStore.user.nickname,
    inspectionDate: new Date().getTime(),
    status: undefined,
    result: undefined,
    remark: undefined,
  }
  formRef.value?.resetFields()
}
</script>
